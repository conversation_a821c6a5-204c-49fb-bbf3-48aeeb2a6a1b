<template>
  <view class="message-page">
    <scroll-view scroll-y>
      <!-- 消息列表 -->
      <view class="message-item" v-for="(item, index) in messageList" :key="item.id" @tap="onMessageTap(item)">
        <!-- 头像 -->
        <view class="avatar-container">
          <image class="avatar" :src="getUserAvatar(item.userId)" mode="aspectFill" />
        </view>
        <!-- 消息内容区域 -->
        <view class="message-content">
          <!-- 用户名和时间 -->
          <view class="message-header">
            <text class="username">{{ getUserName(item.userId) }}</text>
            <text class="time">{{ formatTime(item.createTime) }}</text>
          </view>

          <!-- 最后一条消息内容 -->
          <view class="last-message">
            <text class="message-text">{{ getMessageContent(item) }}</text>
          </view>
        </view>

        <!-- 未读消息数量 -->
        <view class="unread-badge" v-if="item.unreadCount && item.unreadCount > 0">
          <text class="unread-text">{{ item.unreadCount > 99 ? '99+' : item.unreadCount }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      messageList: [
        {
          id: '1921827090039152642_000001',
          createBy: null,
          createTime: '2025-01-12 15:17:51',
          updateBy: null,
          updateTime: null,
          sysOrgCode: null,
          userId: '1921822887908581378',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '你好，最近怎么样？',
          status: '正常',
          unreadCount: 2
        },
        {
          id: '1921827090039152642_000002',
          createBy: null,
          createTime: '2025-01-12 14:18:22',
          updateBy: null,
          updateTime: null,
          sysOrgCode: null,
          userId: '1921822887908581377',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '好的，没问题',
          status: '正常',
          unreadCount: 0
        },
        {
          id: '1921827090039152642_000003',
          createBy: null,
          createTime: '2025-01-11 20:30:15',
          updateBy: null,
          updateTime: null,
          sysOrgCode: null,
          userId: '1921822887908581379',
          groupId: '1921827090039152643',
          msgType: 'image',
          content: '',
          status: '正常',
          unreadCount: 5
        },
        {
          id: '1921827090039152642_000004',
          createBy: null,
          createTime: '2025-01-11 16:45:30',
          updateBy: null,
          updateTime: null,
          sysOrgCode: null,
          userId: '1921822887908581380',
          groupId: '1921827090039152644',
          msgType: 'audio',
          content: '',
          status: '正常',
          unreadCount: 1
        },
        {
          id: '1921827090039152642_000005',
          createBy: null,
          createTime: '2025-01-10 09:20:45',
          updateBy: null,
          updateTime: null,
          sysOrgCode: null,
          userId: '1921822887908581381',
          groupId: '1921827090039152645',
          msgType: 'text',
          content: '明天的会议记得参加哦',
          status: '正常',
          unreadCount: 0
        }
      ],
      // 用户信息映射，实际项目中应该从接口获取
      userMap: {
        '1921822887908581378': {
          name: '张三',
          avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=张'
        },
        '1921822887908581377': {
          name: '李四',
          avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=李'
        },
        '1921822887908581379': {
          name: '王五',
          avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'
        },
        '1921822887908581380': {
          name: '赵六',
          avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'
        },
        '1921822887908581381': {
          name: '项目组',
          avatar: 'https://via.placeholder.com/96x96/607D8B/FFFFFF?text=组'
        }
      }
    }
  },
  computed: {},
  watch: {},
  mounted() {},
  methods: {
    // 点击消息项
    onMessageTap(item) {
      console.log('点击消息:', item)
      // 这里可以跳转到聊天页面
      uni.navigateTo({
        url: `/pagesHuYunIm/pages/chat/index?groupId=${item.groupId}&userId=${item.userId}`
      })
    },

    // 获取用户头像
    getUserAvatar(userId) {
      return this.userMap[userId]?.avatar || 'https://via.placeholder.com/96x96/9E9E9E/FFFFFF?text=?'
    },

    // 获取用户名
    getUserName(userId) {
      return this.userMap[userId]?.name || '未知用户'
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return ''

      const now = new Date()
      const msgTime = new Date(timeStr)
      const diff = now.getTime() - msgTime.getTime()

      // 今天
      if (diff < 24 * 60 * 60 * 1000 && now.getDate() === msgTime.getDate()) {
        return msgTime.toTimeString().slice(0, 5) // HH:MM
      }

      // 昨天
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      if (yesterday.getDate() === msgTime.getDate()) {
        return '昨天'
      }

      // 更早的时间
      return `${msgTime.getMonth() + 1}/${msgTime.getDate()}`
    },

    // 获取消息内容预览
    getMessageContent(item) {
      switch (item.msgType) {
        case 'text':
          return item.content || ''
        case 'image':
          return '[图片]'
        case 'audio':
          return '[语音]'
        case 'video':
          return '[视频]'
        case 'file':
          return '[文件]'
        default:
          return '[消息]'
      }
    }
  },
  onLoad() {}
}
</script>
<style lang="scss" scoped>
.message-page {
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}

.nav-bar {
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}

.message-list {
  flex: 1;
  background-color: #ffffff;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  transition: background-color 0.2s;

  &:active {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

.avatar-container {
  margin-right: 24rpx;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}

.message-content {
  flex: 1;
  min-width: 0; // 防止文本溢出
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.username {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time {
  font-size: 24rpx;
  color: #999999;
  flex-shrink: 0;
  margin-left: 16rpx;
}

.last-message {
  display: flex;
  align-items: center;
}

.message-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.unread-badge {
  margin-left: 16rpx;
  background-color: #ff4444;
  border-radius: 20rpx;
  min-width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.unread-text {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 1;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .message-item {
    padding: 20rpx 24rpx;
  }

  .avatar {
    width: 88rpx;
    height: 88rpx;
  }

  .username {
    font-size: 30rpx;
    max-width: 300rpx;
  }

  .message-text {
    font-size: 26rpx;
  }
}
</style>
