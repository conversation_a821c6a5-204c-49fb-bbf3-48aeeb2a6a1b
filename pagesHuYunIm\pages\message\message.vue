<template>
  <view class="chat-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @tap="goBack">
        <text class="nav-back">‹</text>
      </view>
      <view class="nav-center">
        <text class="nav-title">{{ groupInfo.name }}</text>
        <text class="nav-subtitle">{{ groupInfo.memberCount }}人</text>
      </view>
      <view class="nav-right" @tap="showGroupInfo">
        <text class="nav-more">⋯</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view class="message-list" scroll-y :scroll-top="scrollTop" scroll-with-animation @scrolltoupper="loadMoreMessages">
      <!-- 加载更多提示 -->
      <view class="load-more" v-if="loading">
        <text class="load-text">加载中...</text>
      </view>

      <!-- 消息项 -->
      <view class="message-wrapper" v-for="(item, index) in messageList" :key="item.id">
        <!-- 时间分隔线 -->
        <view class="time-divider" v-if="showTime(item, index)">
          <text class="time-text">{{ formatMessageTime(item.createTime) }}</text>
        </view>
        <!-- 消息内容 -->
        <view class="message-item" :class="{ 'is-self': isSelfMessage(item) }">
          <!-- 左侧头像（对方消息） -->
          <view class="avatar-container" v-if="!isSelfMessage(item)">
            <image class="avatar" :src="getUserAvatar(item.userId)" mode="aspectFill" @tap="showUserProfile(item.userId)" />
          </view>
          <!-- 消息主体 -->
          <view class="message-body">
            <!-- 昵称（群聊中对方消息显示） -->
            <view class="nickname" v-if="!isSelfMessage(item) && isGroupChat">
              {{ getUserName(item.userId) }}
            </view>
            <!-- 消息气泡 -->
            <view class="message-bubble" :class="getBubbleClass(item)" @longpress="showMessageMenu(item)">
              <!-- 文本消息 -->
              <view class="text-content" v-if="item.msgType === 'text'">
                {{ item.content }}
              </view>
              <!-- 图片消息 -->
              <view class="image-content" v-else-if="item.msgType === 'image'">
                <image class="message-image" :src="item.content" mode="aspectFill" @tap="previewImage(item.content)" />
              </view>

              <!-- 语音消息 -->
              <view class="voice-content" v-else-if="item.msgType === 'audio'">
                <view class="voice-icon" :class="{ playing: item.isPlaying }">
                  <text class="voice-waves">🎵</text>
                </view>
                <text class="voice-duration">{{ item.duration || '1' }}"</text>
              </view>

              <!-- 其他类型消息 -->
              <view class="other-content" v-else>
                <text class="other-text">{{ getMessageContent(item) }}</text>
              </view>
            </view>

            <!-- 消息状态（自己的消息） -->
            <view class="message-status" v-if="isSelfMessage(item)">
              <text class="status-text" v-if="item.status === 'sending'">发送中</text>
              <text class="status-text" v-else-if="item.status === 'failed'">发送失败</text>
            </view>
          </view>

          <!-- 右侧头像（自己的消息） -->
          <view class="avatar-container" v-if="isSelfMessage(item)">
            <image class="avatar" :src="selfAvatar" mode="aspectFill" />
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="input-area">
      <view class="input-container">
        <view class="input-tools">
          <view class="tool-item" @tap="showEmojiPanel">
            <text class="tool-icon">😊</text>
          </view>
        </view>

        <view class="input-wrapper">
          <textarea
            class="message-input"
            v-model="inputText"
            placeholder="输入消息..."
            :auto-height="true"
            :max-height="120"
            @focus="onInputFocus"
            @blur="onInputBlur"
            @confirm="sendMessage"
          />
        </view>

        <view class="input-actions">
          <view class="action-item" @tap="chooseImage" v-if="!inputText.trim()">
            <text class="action-icon">📷</text>
          </view>
          <view class="action-item" @tap="recordVoice" v-if="!inputText.trim()">
            <text class="action-icon">🎤</text>
          </view>
          <view class="send-btn" @tap="sendMessage" v-if="inputText.trim()">
            <text class="send-text">发送</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      // 群聊信息
      groupInfo: {
        id: '1921827090039152642',
        name: '项目讨论组',
        memberCount: 5
      },

      // 当前用户信息
      selfUserId: '1921822887908581378',
      selfAvatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=我',

      // 消息列表
      messageList: [
        {
          id: '1921827090039152642_000001',
          createTime: '2025-01-12 15:17:51',
          userId: '1921822887908581377',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '大家好，今天的项目进度怎么样？',
          status: 'success'
        },
        {
          id: '1921827090039152642_000002',
          createTime: '2025-01-12 15:18:22',
          userId: '1921822887908581378',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '我这边基本完成了，正在测试',
          status: 'success'
        },
        {
          id: '1921827090039152642_000003',
          createTime: '2025-01-12 15:19:15',
          userId: '1921822887908581379',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '我还需要一点时间，预计明天完成',
          status: 'success'
        },
        {
          id: '1921827090039152642_000004',
          createTime: '2025-01-12 15:20:30',
          userId: '1921822887908581380',
          groupId: '1921827090039152642',
          msgType: 'image',
          content: 'https://via.placeholder.com/200x150/FF5722/FFFFFF?text=截图',
          status: 'success'
        },
        {
          id: '1921827090039152642_000005',
          createTime: '2025-01-12 15:21:45',
          userId: '1921822887908581378',
          groupId: '1921827090039152642',
          msgType: 'text',
          content: '看起来不错！',
          status: 'success'
        }
      ],

      // 用户信息映射
      userMap: {
        '1921822887908581377': {
          name: '张三',
          avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=张'
        },
        '1921822887908581378': {
          name: '李四',
          avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'
        },
        '1921822887908581379': {
          name: '王五',
          avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'
        },
        '1921822887908581380': {
          name: '赵六',
          avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'
        },
        '1921822887908581381': {
          name: '项目组',
          avatar: 'https://via.placeholder.com/96x96/607D8B/FFFFFF?text=组'
        }
      },

      // 界面状态
      inputText: '',
      scrollTop: 0,
      loading: false,
      isGroupChat: true,
      showEmojiPanel: false,

      // 分页参数
      page: 1,
      pageSize: 20,
      hasMore: true
    }
  },

  computed: {},

  watch: {
    messageList: {
      handler() {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      },
      deep: true
    }
  },

  mounted() {
    this.scrollToBottom()
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 显示群信息
    showGroupInfo() {
      console.log('显示群信息')
      // 这里可以跳转到群信息页面
    },

    // 判断是否为自己发送的消息
    isSelfMessage(item) {
      return item.userId === this.selfUserId
    },

    // 获取用户头像
    getUserAvatar(userId) {
      return this.userMap[userId]?.avatar || 'https://via.placeholder.com/96x96/9E9E9E/FFFFFF?text=?'
    },

    // 获取用户名
    getUserName(userId) {
      return this.userMap[userId]?.name || '未知用户'
    },

    // 是否显示时间分隔线
    showTime(item, index) {
      if (index === 0) return true

      const prevItem = this.messageList[index - 1]
      const currentTime = new Date(item.createTime).getTime()
      const prevTime = new Date(prevItem.createTime).getTime()

      // 超过5分钟显示时间
      return currentTime - prevTime > 5 * 60 * 1000
    },

    // 格式化消息时间
    formatMessageTime(timeStr) {
      if (!timeStr) return ''

      const now = new Date()
      const msgTime = new Date(timeStr)
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const msgDate = new Date(msgTime.getFullYear(), msgTime.getMonth(), msgTime.getDate())

      const diffDays = Math.floor((today - msgDate) / (24 * 60 * 60 * 1000))

      if (diffDays === 0) {
        // 今天，显示时间
        return msgTime.toTimeString().slice(0, 5)
      } else if (diffDays === 1) {
        // 昨天
        return `昨天 ${msgTime.toTimeString().slice(0, 5)}`
      } else if (diffDays < 7) {
        // 一周内，显示星期
        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
        return `${weekdays[msgTime.getDay()]} ${msgTime.toTimeString().slice(0, 5)}`
      } else {
        // 更早，显示日期
        return `${msgTime.getMonth() + 1}月${msgTime.getDate()}日 ${msgTime.toTimeString().slice(0, 5)}`
      }
    },

    // 获取消息气泡样式类
    getBubbleClass(item) {
      const classes = []
      if (this.isSelfMessage(item)) {
        classes.push('bubble-self')
      } else {
        classes.push('bubble-other')
      }

      if (item.msgType === 'image') {
        classes.push('bubble-image')
      }

      return classes.join(' ')
    },

    // 获取消息内容预览
    getMessageContent(item) {
      switch (item.msgType) {
        case 'text':
          return item.content || ''
        case 'image':
          return '[图片]'
        case 'audio':
          return '[语音]'
        case 'video':
          return '[视频]'
        case 'file':
          return '[文件]'
        default:
          return '[消息]'
      }
    },

    // 显示用户资料
    showUserProfile(userId) {
      console.log('显示用户资料:', userId)
      // 这里可以跳转到用户资料页面
    },

    // 显示消息菜单
    showMessageMenu(item) {
      console.log('显示消息菜单:', item)
      uni.showActionSheet({
        itemList: ['复制', '删除', '撤回'],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.copyMessage(item)
              break
            case 1:
              this.deleteMessage(item)
              break
            case 2:
              this.recallMessage(item)
              break
          }
        }
      })
    },

    // 复制消息
    copyMessage(item) {
      if (item.msgType === 'text') {
        uni.setClipboardData({
          data: item.content,
          success: () => {
            uni.showToast({
              title: '已复制',
              icon: 'success'
            })
          }
        })
      }
    },

    // 删除消息
    deleteMessage(item) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            const index = this.messageList.findIndex((msg) => msg.id === item.id)
            if (index > -1) {
              this.messageList.splice(index, 1)
            }
          }
        }
      })
    },

    // 撤回消息
    recallMessage(item) {
      if (!this.isSelfMessage(item)) {
        uni.showToast({
          title: '只能撤回自己的消息',
          icon: 'none'
        })
        return
      }

      // 检查时间限制（比如2分钟内）
      const now = new Date().getTime()
      const msgTime = new Date(item.createTime).getTime()
      if (now - msgTime > 2 * 60 * 1000) {
        uni.showToast({
          title: '超过时间限制，无法撤回',
          icon: 'none'
        })
        return
      }

      uni.showModal({
        title: '确认撤回',
        content: '确定要撤回这条消息吗？',
        success: (res) => {
          if (res.confirm) {
            // 这里应该调用撤回消息的API
            item.recalled = true
            uni.showToast({
              title: '已撤回',
              icon: 'success'
            })
          }
        }
      })
    },

    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      })
    },

    // 加载更多消息
    loadMoreMessages() {
      if (this.loading || !this.hasMore) return

      this.loading = true
      console.log('加载更多消息...')

      // 模拟加载更多消息
      setTimeout(() => {
        this.loading = false
        // 这里应该调用API加载历史消息
      }, 1000)
    },

    // 输入框获得焦点
    onInputFocus() {
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 输入框失去焦点
    onInputBlur() {
      // 可以在这里处理一些逻辑
    },

    // 发送消息
    sendMessage() {
      const text = this.inputText.trim()
      if (!text) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        })
        return
      }

      const message = {
        id: Date.now().toString(),
        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId: this.selfUserId,
        groupId: this.groupInfo.id,
        msgType: 'text',
        content: text,
        status: 'sending'
      }

      // 添加到消息列表
      this.messageList.push(message)
      this.inputText = ''

      // 模拟发送过程
      setTimeout(() => {
        message.status = 'success'
        // 这里应该调用发送消息的API
      }, 1000)
    },

    // 选择图片
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sourceType: ['camera', 'album'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          this.sendImageMessage(tempFilePath)
        }
      })
    },

    // 发送图片消息
    sendImageMessage(imagePath) {
      const message = {
        id: Date.now().toString(),
        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId: this.selfUserId,
        groupId: this.groupInfo.id,
        msgType: 'image',
        content: imagePath,
        status: 'sending'
      }

      this.messageList.push(message)

      // 模拟上传过程
      setTimeout(() => {
        message.status = 'success'
        // 这里应该调用上传图片和发送消息的API
      }, 2000)
    },

    // 录制语音
    recordVoice() {
      uni.showToast({
        title: '长按录音功能待开发',
        icon: 'none'
      })
    },

    // 显示表情面板
    handleShowEmojiPanel() {
      uni.showToast({
        title: '表情功能待开发',
        icon: 'none'
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        this.scrollTop = this.messageList.length * 1000
      })
    }
  },

  onLoad(options) {
    // 从参数中获取群组信息
    if (options.groupId) {
      this.groupInfo.id = options.groupId
    }
    if (options.groupName) {
      this.groupInfo.name = decodeURIComponent(options.groupName)
    }
  }
}
</script>
<style lang="scss" scoped>
.chat-page {
  height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}

// 顶部导航栏
.nav-bar {
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left,
.nav-right {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back {
  font-size: 48rpx;
  color: #007aff;
  font-weight: 300;
}

.nav-more {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

.nav-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
}

.nav-subtitle {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.2;
}

// 消息列表
.message-list {
  flex: 1;
  background-color: #f8f8f8;
  padding: 0 24rpx;
}

.load-more {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.load-text {
  font-size: 28rpx;
  color: #999999;
}

// 时间分隔线
.time-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx 0 16rpx;
}

.time-text {
  background-color: rgba(0, 0, 0, 0.1);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

// 消息项
.message-wrapper {
  margin-bottom: 24rpx;
}

.message-item {
  display: flex;
  align-items: flex-start;

  &.is-self {
    flex-direction: row-reverse;

    .message-body {
      align-items: flex-end;
    }

    .avatar-container {
      margin-left: 16rpx;
      margin-right: 0;
    }
  }
}

.avatar-container {
  margin-right: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

.message-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: calc(100% - 120rpx);
}

.nickname {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
  padding: 0 16rpx;
}

// 消息气泡
.message-bubble {
  max-width: 100%;
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  position: relative;
  word-wrap: break-word;

  &.bubble-self {
    background-color: #95ec69;
    color: #000000;

    &::after {
      content: '';
      position: absolute;
      right: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border: 12rpx solid transparent;
      border-left-color: #95ec69;
    }
  }

  &.bubble-other {
    background-color: #ffffff;
    color: #000000;

    &::before {
      content: '';
      position: absolute;
      left: -12rpx;
      top: 20rpx;
      width: 0;
      height: 0;
      border: 12rpx solid transparent;
      border-right-color: #ffffff;
    }
  }

  &.bubble-image {
    padding: 8rpx;
    border-radius: 12rpx;
  }
}

.text-content {
  font-size: 32rpx;
  line-height: 1.4;
}

.image-content {
  border-radius: 8rpx;
  overflow: hidden;
}

.message-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
}

.voice-content {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.voice-icon {
  margin-right: 16rpx;

  &.playing {
    animation: voice-wave 1s infinite;
  }
}

.voice-waves {
  font-size: 32rpx;
}

.voice-duration {
  font-size: 28rpx;
  color: #666666;
}

.other-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60rpx;
}

.other-text {
  font-size: 28rpx;
  color: #666666;
}

.message-status {
  margin-top: 8rpx;
  padding: 0 16rpx;
}

.status-text {
  font-size: 24rpx;
  color: #999999;
}

// 底部输入区域
.input-area {
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
}

.input-tools {
  display: flex;
  align-items: center;
}

.tool-item {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background-color: #f5f5f5;

  &:active {
    background-color: #e5e5e5;
  }
}

.tool-icon {
  font-size: 32rpx;
}

.input-wrapper {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
}

.message-input {
  width: 100%;
  font-size: 32rpx;
  line-height: 1.4;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  min-height: 32rpx;
  max-height: 120rpx;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.action-item {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background-color: #f5f5f5;

  &:active {
    background-color: #e5e5e5;
  }
}

.action-icon {
  font-size: 32rpx;
}

.send-btn {
  background-color: #07c160;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  min-width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    background-color: #06ad56;
  }
}

.send-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}

// 动画效果
@keyframes voice-wave {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .nav-bar {
    height: 80rpx;
    padding: 0 24rpx;
  }

  .nav-title {
    font-size: 30rpx;
  }

  .nav-subtitle {
    font-size: 22rpx;
  }

  .message-list {
    padding: 0 20rpx;
  }

  .avatar {
    width: 72rpx;
    height: 72rpx;
  }

  .text-content {
    font-size: 30rpx;
  }

  .message-image {
    width: 180rpx;
    height: 135rpx;
  }

  .input-area {
    padding: 12rpx 20rpx;
  }

  .message-input {
    font-size: 30rpx;
  }

  .send-text {
    font-size: 30rpx;
  }
}
</style>
