<template>
  <view class="page">
    <!-- 会话列表 -->
    <scroll-view class="chat-list" scroll-y="true" :show-scrollbar="false">
      <view class="list-item" v-for="(item, index) in users" :key="index" @click="connect(item)">
        <view class="avatar-container">
          <!-- 未读消息红点 -->
          <view class="unread-badge" v-if="item.notReadNum > 0">
            <text class="unread-count" v-if="item.notReadNum <= 99">{{ item.notReadNum }}</text>
            <text class="unread-count" v-else>99+</text>
          </view>
          <image class="avatar" :src="item.avatar || defaultAvatar" mode="aspectFill"></image>
        </view>
        <view class="content">
          <view class="title-row">
            <text class="name">{{ item.title }}</text>
            <text class="time" v-if="item.updateTime">{{ formatTime(item.updateTime) }}</text>
          </view>
          <view class="message-row">
            <view class="last-message">
              <!-- 文本消息 -->
              <text v-if="item.lastMsg && item.lastMsg.msgType == 'text'" class="message-text">
                {{ getMessagePreview(item.lastMsg) }}
              </text>
              <!-- 图片消息 -->
              <text v-else-if="item.lastMsg && item.lastMsg.msgType == 'image'" class="message-text">[图片]</text>
              <!-- 语音消息 -->
              <text v-else-if="item.lastMsg && item.lastMsg.msgType == 'voice'" class="message-text">[语音]</text>
              <!-- 视频消息 -->
              <text v-else-if="item.lastMsg && item.lastMsg.msgType == 'video'" class="message-text">[视频]</text>
              <!-- 文件消息 -->
              <text v-else-if="item.lastMsg && item.lastMsg.msgType == 'file'" class="message-text">[文件]</text>
              <!-- 默认消息 -->
              <text v-else class="message-text placeholder">暂无消息</text>
            </view>
            <!-- 消息状态指示器 -->
            <view class="message-status" v-if="item.lastMsg">
              <text class="mute-icon" v-if="item.isMuted">🔕</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 空状态 -->
      <view class="empty-state" v-if="users.length === 0">
        <text class="empty-icon">💬</text>
        <text class="empty-text">暂无会话</text>
        <text class="empty-desc">开始一段新的对话吧</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
// 导入新的MQTT工具包
import mqttClient from '../../utils/mqttClient.js'
import { createUserInfo, MESSAGE_TYPES } from '../../utils/mqttConfig.js'
import mqtt from '../../lib/mqtt.min.js'
import { listUser } from '../../api/public.js'
import Cache from '../../utils/cache.js'

export default {
  data() {
    return {
      userMap: {},
      users: [],
      defaultAvatar: 'https://dummyimage.com/100x100/cccccc/ffffff?text=头像'
    }
  },

  onShow() {
    this.loadData()
    this.initMqtt()
  },

  onLoad() {
    // 设置MQTT库
    mqttClient.setMqttLib(mqtt)
  },

  onUnload() {
    // 页面卸载时断开MQTT连接
    mqttClient.disconnect()
  },

  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData()
  },

  methods: {
    /**
     * 初始化MQTT连接
     */
    async initMqtt() {
      try {
        // 从store获取用户信息，如果没有则使用默认值
        const userInfo = createUserInfo(
          '1921822887908581377', // userId
          '范发发', // nickname
          'hbs119', // channelCode
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1ODUyOX0.S24GcZb6K88dr6FdH82v9oqvTbBqVj9u9xOvd2b30_Y', // token
          'https://dummyimage.com/100x100/cccccc/ffffff?text=头像', // avatar
          'DEV' // 环境
        )
        // 创建用户信息对象
        const mqttUserInfo = createUserInfo(
          userInfo.userId,
          userInfo.nickname || '用户',
          userInfo.channelCode,
          userInfo.token,
          userInfo.avatar || this.defaultAvatar,
          'DEV'
        )

        const callbacks = {
          onConnect: () => {
            console.log('MQTT连接成功')
            this.isConnected = true
          },
          onMessage: (topic, mqttMsg) => {
            this.handleMqttMessage(mqttMsg)
          },
          onReconnect: () => {
            console.log('MQTT重连中...')
            this.isConnected = false
          },
          onError: (error) => {
            console.error('MQTT连接错误:', error)
            this.isConnected = false
          },
          onEnd: () => {
            console.log('MQTT连接已断开')
            this.isConnected = false
          }
        }

        // 连接MQTT
        mqttClient.connect(mqttUserInfo, callbacks)
      } catch (error) {
        console.error('初始化MQTT失败:', error)
        this.isConnected = false
      }
    },

    /**
     * 处理MQTT消息
     */
    handleMqttMessage(mqttMsg) {
      if (mqttMsg.command === MESSAGE_TYPES.CHAT_MSG) {
        const chatMsg = mqttMsg.data

        // 设置用户昵称
        if (this.userMap.hasOwnProperty(chatMsg.userId)) {
          chatMsg.nickname = this.userMap[chatMsg.userId].nickname
        }

        // 更新用户列表中的消息信息
        for (let i = 0; i < this.users.length; i++) {
          if (this.users[i].id === chatMsg.groupId) {
            this.users[i].lastMsg = chatMsg
            this.users[i].updateTime = chatMsg.createTime
            this.users[i].notReadNum = (this.users[i].notReadNum || 0) + 1

            // 将更新的会话移到顶部
            const updatedUser = this.users.splice(i, 1)[0]
            this.users.unshift(updatedUser)
            break
          }
        }

        console.log('收到聊天消息:', chatMsg)
      }
    },

    /**
     * 加载用户数据
     */
    async loadData() {
      try {
        const [res, err] = await listUser({})

        if (res) {
          // 构建用户映射表
          for (let i = 0; i < res.length; i++) {
            let userArr = res[i].userArr
            if (userArr) {
              for (let j = 0; j < userArr.length; j++) {
                let userItem = userArr[j]
                this.userMap[userItem.userId] = userItem
              }
            }

            // 设置最后一条消息的昵称
            if (res[i].lastMsg && res[i].lastMsg.userId) {
              const userId = res[i].lastMsg.userId
              if (this.userMap[userId]) {
                res[i].lastMsg.nickname = this.userMap[userId].nickname
              }
            }

            // 初始化未读消息数
            if (!res[i].notReadNum) {
              res[i].notReadNum = 0
            }
          }

          // 按更新时间排序
          res.sort((a, b) => {
            const timeA = a.updateTime || 0
            const timeB = b.updateTime || 0
            return timeB - timeA
          })
          this.users = res
          // 缓存用户映射表
          Cache.set('userMap', JSON.stringify(this.userMap))
        }

        if (err) {
          console.error('加载用户数据失败:', err)
        }
      } catch (error) {
        console.error('加载数据异常:', error)
      }
    },

    /**
     * 点击会话项
     */
    connect(item) {
      // 断开当前MQTT连接
      mqttClient.disconnect()
      // 清除未读消息数
      item.notReadNum = 0
      // 跳转到聊天页面
      uni.navigateTo({
        url: `/pagesHuYunIm/pages/chat/index?groupId=${item.id}&name=${encodeURIComponent(item.title)}&avatar=${encodeURIComponent(
          item.avatar || this.defaultAvatar
        )}`,
        fail: (err) => {
          console.error('跳转失败:', err)
        }
      })
    },

    /**
     * 格式化时间显示
     */
    formatTime(timestamp) {
      if (!timestamp) return ''

      const now = new Date()
      const msgTime = new Date(timestamp)
      const diff = now.getTime() - msgTime.getTime()

      // 今天
      if (now.toDateString() === msgTime.toDateString()) {
        return msgTime.toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        })
      }

      // 昨天
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      if (yesterday.toDateString() === msgTime.toDateString()) {
        return '昨天'
      }

      // 一周内
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        return weekdays[msgTime.getDay()]
      }

      // 更早
      return msgTime.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit'
      })
    },

    /**
     * 获取消息预览文本
     */
    getMessagePreview(lastMsg) {
      if (!lastMsg) return ''

      const nickname = lastMsg.nickname || '未知用户'
      const content = lastMsg.content || ''

      // 限制显示长度
      const maxLength = 30
      const preview = `${nickname}: ${content}`

      if (preview.length > maxLength) {
        return preview.substring(0, maxLength) + '...'
      }

      return preview
    },

    /**
     * 刷新数据
     */
    async refreshData() {
      try {
        await this.loadData()
        uni.showToast({
          title: '刷新成功',
          icon: 'success',
          duration: 1500
        })
      } catch (error) {
        console.error('刷新失败:', error)
        uni.showToast({
          title: '刷新失败',
          icon: 'none',
          duration: 1500
        })
      } finally {
        // 停止下拉刷新
        uni.stopPullDownRefresh()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  height: 100vh;
  background-color: #f7f7f7;
  display: flex;
  flex-direction: column;
}
/* 会话列表样式 */
.chat-list {
  flex: 1;
  background-color: #ffffff;
}
.list-item {
  display: flex;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  transition: background-color 0.2s;

  &:active {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

/* 头像容器样式 */
.avatar-container {
  position: relative;
  margin-right: 24rpx;

  .avatar {
    width: 96rpx;
    height: 96rpx;
    border-radius: 12rpx;
    background-color: #f0f0f0;
  }

  .unread-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background-color: #ff4444;
    border-radius: 20rpx;
    min-width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ffffff;

    .unread-count {
      color: #ffffff;
      font-size: 20rpx;
      font-weight: bold;
      line-height: 1;
      padding: 0 8rpx;
    }
  }
}

/* 内容区域样式 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;

  .name {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .time {
    font-size: 24rpx;
    color: #999999;
    margin-left: 16rpx;
    flex-shrink: 0;
  }
}

.message-row {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .last-message {
    flex: 1;
    min-width: 0;

    .message-text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.placeholder {
        color: #cccccc;
        font-style: italic;
      }
    }
  }

  .message-status {
    margin-left: 16rpx;

    .mute-icon {
      font-size: 24rpx;
      color: #999999;
    }
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 32rpx;
    opacity: 0.3;
  }

  .empty-text {
    font-size: 32rpx;
    color: #666666;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #999999;
  }
}

/* 连接状态指示器 */
.connection-status {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  z-index: 1000;

  .status-text {
    font-size: 28rpx;
  }
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .list-item {
    padding: 20rpx 24rpx;
  }

  .avatar-container {
    margin-right: 20rpx;

    .avatar {
      width: 80rpx;
      height: 80rpx;
    }
  }

  .title-row .name {
    font-size: 30rpx;
  }

  .message-row .message-text {
    font-size: 26rpx;
  }
}
</style>
