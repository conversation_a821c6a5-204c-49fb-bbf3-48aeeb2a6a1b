(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesHuYunIm/pages/message/message"],{

/***/ 792:
/*!************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/main.js?{"page":"pagesHuYunIm%2Fpages%2Fmessage%2Fmessage"} ***!
  \************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _message = _interopRequireDefault(__webpack_require__(/*! ./pagesHuYunIm/pages/message/message.vue */ 793));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_message.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 793:
/*!***************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue ***!
  \***************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./message.vue?vue&type=template&id=0b926c58&scoped=true& */ 794);
/* harmony import */ var _message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./message.vue?vue&type=script&lang=js& */ 796);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true& */ 798);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 43);

var renderjs





/* normalize component */

var component = Object(_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "0b926c58",
  null,
  false,
  _message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesHuYunIm/pages/message/message.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 794:
/*!**********************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=template&id=0b926c58&scoped=true& ***!
  \**********************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=0b926c58&scoped=true& */ 795);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_template_id_0b926c58_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 795:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=template&id=0b926c58&scoped=true& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var render = function () {}
var staticRenderFns = []
var recyclableRender
var components



/***/ }),

/***/ 796:
/*!****************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js& */ 797);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 797:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=script&lang=js& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var _default = {
  data: function data() {
    return {
      // 群聊信息
      groupInfo: {
        id: '1921827090039152642',
        name: '项目讨论组',
        memberCount: 5
      },
      // 当前用户信息
      selfUserId: '1921822887908581378',
      selfAvatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=我',
      // 消息列表
      messageList: [{
        id: '1921827090039152642_000001',
        createTime: '2025-01-12 15:17:51',
        userId: '1921822887908581377',
        groupId: '1921827090039152642',
        msgType: 'text',
        content: '大家好，今天的项目进度怎么样？',
        status: 'success'
      }, {
        id: '1921827090039152642_000002',
        createTime: '2025-01-12 15:18:22',
        userId: '1921822887908581378',
        groupId: '1921827090039152642',
        msgType: 'text',
        content: '我这边基本完成了，正在测试',
        status: 'success'
      }, {
        id: '1921827090039152642_000003',
        createTime: '2025-01-12 15:19:15',
        userId: '1921822887908581379',
        groupId: '1921827090039152642',
        msgType: 'text',
        content: '我还需要一点时间，预计明天完成',
        status: 'success'
      }, {
        id: '1921827090039152642_000004',
        createTime: '2025-01-12 15:20:30',
        userId: '1921822887908581380',
        groupId: '1921827090039152642',
        msgType: 'image',
        content: 'https://via.placeholder.com/200x150/FF5722/FFFFFF?text=截图',
        status: 'success'
      }, {
        id: '1921827090039152642_000005',
        createTime: '2025-01-12 15:21:45',
        userId: '1921822887908581378',
        groupId: '1921827090039152642',
        msgType: 'text',
        content: '看起来不错！',
        status: 'success'
      }],
      // 用户信息映射
      userMap: {
        '1921822887908581377': {
          name: '张三',
          avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=张'
        },
        '1921822887908581378': {
          name: '李四',
          avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'
        },
        '1921822887908581379': {
          name: '王五',
          avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'
        },
        '1921822887908581380': {
          name: '赵六',
          avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'
        },
        '1921822887908581381': {
          name: '项目组',
          avatar: 'https://via.placeholder.com/96x96/607D8B/FFFFFF?text=组'
        }
      },
      // 界面状态
      inputText: '',
      scrollTop: 0,
      loading: false,
      isGroupChat: true,
      showEmojiPanel: false,
      // 分页参数
      page: 1,
      pageSize: 20,
      hasMore: true
    };
  },
  computed: {},
  watch: {
    messageList: {
      handler: function handler() {
        var _this = this;
        this.$nextTick(function () {
          _this.scrollToBottom();
        });
      },
      deep: true
    }
  },
  mounted: function mounted() {
    this.scrollToBottom();
  },
  methods: {
    // 返回上一页
    goBack: function goBack() {
      uni.navigateBack();
    },
    // 显示群信息
    showGroupInfo: function showGroupInfo() {
      console.log('显示群信息');
      // 这里可以跳转到群信息页面
    },
    // 判断是否为自己发送的消息
    isSelfMessage: function isSelfMessage(item) {
      return item.userId === this.selfUserId;
    },
    // 获取用户头像
    getUserAvatar: function getUserAvatar(userId) {
      var _this$userMap$userId;
      return ((_this$userMap$userId = this.userMap[userId]) === null || _this$userMap$userId === void 0 ? void 0 : _this$userMap$userId.avatar) || 'https://via.placeholder.com/96x96/9E9E9E/FFFFFF?text=?';
    },
    // 获取用户名
    getUserName: function getUserName(userId) {
      var _this$userMap$userId2;
      return ((_this$userMap$userId2 = this.userMap[userId]) === null || _this$userMap$userId2 === void 0 ? void 0 : _this$userMap$userId2.name) || '未知用户';
    },
    // 是否显示时间分隔线
    showTime: function showTime(item, index) {
      if (index === 0) return true;
      var prevItem = this.messageList[index - 1];
      var currentTime = new Date(item.createTime).getTime();
      var prevTime = new Date(prevItem.createTime).getTime();

      // 超过5分钟显示时间
      return currentTime - prevTime > 5 * 60 * 1000;
    },
    // 格式化消息时间
    formatMessageTime: function formatMessageTime(timeStr) {
      if (!timeStr) return '';
      var now = new Date();
      var msgTime = new Date(timeStr);
      var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      var msgDate = new Date(msgTime.getFullYear(), msgTime.getMonth(), msgTime.getDate());
      var diffDays = Math.floor((today - msgDate) / (24 * 60 * 60 * 1000));
      if (diffDays === 0) {
        // 今天，显示时间
        return msgTime.toTimeString().slice(0, 5);
      } else if (diffDays === 1) {
        // 昨天
        return "\u6628\u5929 ".concat(msgTime.toTimeString().slice(0, 5));
      } else if (diffDays < 7) {
        // 一周内，显示星期
        var weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
        return "".concat(weekdays[msgTime.getDay()], " ").concat(msgTime.toTimeString().slice(0, 5));
      } else {
        // 更早，显示日期
        return "".concat(msgTime.getMonth() + 1, "\u6708").concat(msgTime.getDate(), "\u65E5 ").concat(msgTime.toTimeString().slice(0, 5));
      }
    },
    // 获取消息气泡样式类
    getBubbleClass: function getBubbleClass(item) {
      var classes = [];
      if (this.isSelfMessage(item)) {
        classes.push('bubble-self');
      } else {
        classes.push('bubble-other');
      }
      if (item.msgType === 'image') {
        classes.push('bubble-image');
      }
      return classes.join(' ');
    },
    // 获取消息内容预览
    getMessageContent: function getMessageContent(item) {
      switch (item.msgType) {
        case 'text':
          return item.content || '';
        case 'image':
          return '[图片]';
        case 'audio':
          return '[语音]';
        case 'video':
          return '[视频]';
        case 'file':
          return '[文件]';
        default:
          return '[消息]';
      }
    },
    // 显示用户资料
    showUserProfile: function showUserProfile(userId) {
      console.log('显示用户资料:', userId);
      // 这里可以跳转到用户资料页面
    },
    // 显示消息菜单
    showMessageMenu: function showMessageMenu(item) {
      var _this2 = this;
      console.log('显示消息菜单:', item);
      uni.showActionSheet({
        itemList: ['复制', '删除', '撤回'],
        success: function success(res) {
          switch (res.tapIndex) {
            case 0:
              _this2.copyMessage(item);
              break;
            case 1:
              _this2.deleteMessage(item);
              break;
            case 2:
              _this2.recallMessage(item);
              break;
          }
        }
      });
    },
    // 复制消息
    copyMessage: function copyMessage(item) {
      if (item.msgType === 'text') {
        uni.setClipboardData({
          data: item.content,
          success: function success() {
            uni.showToast({
              title: '已复制',
              icon: 'success'
            });
          }
        });
      }
    },
    // 删除消息
    deleteMessage: function deleteMessage(item) {
      var _this3 = this;
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条消息吗？',
        success: function success(res) {
          if (res.confirm) {
            var index = _this3.messageList.findIndex(function (msg) {
              return msg.id === item.id;
            });
            if (index > -1) {
              _this3.messageList.splice(index, 1);
            }
          }
        }
      });
    },
    // 撤回消息
    recallMessage: function recallMessage(item) {
      if (!this.isSelfMessage(item)) {
        uni.showToast({
          title: '只能撤回自己的消息',
          icon: 'none'
        });
        return;
      }

      // 检查时间限制（比如2分钟内）
      var now = new Date().getTime();
      var msgTime = new Date(item.createTime).getTime();
      if (now - msgTime > 2 * 60 * 1000) {
        uni.showToast({
          title: '超过时间限制，无法撤回',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '确认撤回',
        content: '确定要撤回这条消息吗？',
        success: function success(res) {
          if (res.confirm) {
            // 这里应该调用撤回消息的API
            item.recalled = true;
            uni.showToast({
              title: '已撤回',
              icon: 'success'
            });
          }
        }
      });
    },
    // 预览图片
    previewImage: function previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    },
    // 加载更多消息
    loadMoreMessages: function loadMoreMessages() {
      var _this4 = this;
      if (this.loading || !this.hasMore) return;
      this.loading = true;
      console.log('加载更多消息...');

      // 模拟加载更多消息
      setTimeout(function () {
        _this4.loading = false;
        // 这里应该调用API加载历史消息
      }, 1000);
    },
    // 输入框获得焦点
    onInputFocus: function onInputFocus() {
      var _this5 = this;
      this.$nextTick(function () {
        _this5.scrollToBottom();
      });
    },
    // 输入框失去焦点
    onInputBlur: function onInputBlur() {
      // 可以在这里处理一些逻辑
    },
    // 发送消息
    sendMessage: function sendMessage() {
      var text = this.inputText.trim();
      if (!text) {
        uni.showToast({
          title: '请输入消息内容',
          icon: 'none'
        });
        return;
      }
      var message = {
        id: Date.now().toString(),
        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId: this.selfUserId,
        groupId: this.groupInfo.id,
        msgType: 'text',
        content: text,
        status: 'sending'
      };

      // 添加到消息列表
      this.messageList.push(message);
      this.inputText = '';

      // 模拟发送过程
      setTimeout(function () {
        message.status = 'success';
        // 这里应该调用发送消息的API
      }, 1000);
    },
    // 选择图片
    chooseImage: function chooseImage() {
      var _this6 = this;
      uni.chooseImage({
        count: 1,
        sourceType: ['camera', 'album'],
        success: function success(res) {
          var tempFilePath = res.tempFilePaths[0];
          _this6.sendImageMessage(tempFilePath);
        }
      });
    },
    // 发送图片消息
    sendImageMessage: function sendImageMessage(imagePath) {
      var message = {
        id: Date.now().toString(),
        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
        userId: this.selfUserId,
        groupId: this.groupInfo.id,
        msgType: 'image',
        content: imagePath,
        status: 'sending'
      };
      this.messageList.push(message);

      // 模拟上传过程
      setTimeout(function () {
        message.status = 'success';
        // 这里应该调用上传图片和发送消息的API
      }, 2000);
    },
    // 录制语音
    recordVoice: function recordVoice() {
      uni.showToast({
        title: '长按录音功能待开发',
        icon: 'none'
      });
    },
    // 显示表情面板
    handleShowEmojiPanel: function handleShowEmojiPanel() {
      uni.showToast({
        title: '表情功能待开发',
        icon: 'none'
      });
    },
    // 滚动到底部
    scrollToBottom: function scrollToBottom() {
      var _this7 = this;
      this.$nextTick(function () {
        _this7.scrollTop = _this7.messageList.length * 1000;
      });
    }
  },
  onLoad: function onLoad(options) {
    // 从参数中获取群组信息
    if (options.groupId) {
      this.groupInfo.id = options.groupId;
    }
    if (options.groupName) {
      this.groupInfo.name = decodeURIComponent(options.groupName);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 798:
/*!*************************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true& ***!
  \*************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true& */ 799);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_message_vue_vue_type_style_index_0_id_0b926c58_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 799:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[792,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pagesHuYunIm/pages/message/message.js.map