<view class="message-page data-v-0b926c58"><scroll-view scroll-y="{{true}}" class="data-v-0b926c58"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['onMessageTap',['$0'],[[['messageList','id',item.$orig.id]]]]]]]}}" class="message-item data-v-0b926c58" bindtap="__e"><view class="avatar-container data-v-0b926c58"><image class="avatar data-v-0b926c58" src="{{item.m0}}" mode="aspectFill"></image></view><view class="message-content data-v-0b926c58"><view class="message-header data-v-0b926c58"><text class="username data-v-0b926c58">{{item.m1}}</text><text class="time data-v-0b926c58">{{item.m2}}</text></view><view class="last-message data-v-0b926c58"><text class="message-text data-v-0b926c58">{{item.m3}}</text></view></view><block wx:if="{{item.$orig.unreadCount&&item.$orig.unreadCount>0}}"><view class="unread-badge data-v-0b926c58"><text class="unread-text data-v-0b926c58">{{item.$orig.unreadCount>99?'99+':item.$orig.unreadCount}}</text></view></block></view></block></scroll-view></view>