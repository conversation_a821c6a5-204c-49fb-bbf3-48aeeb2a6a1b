<view class="chat-page data-v-0b926c58"><view class="nav-bar data-v-0b926c58"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-0b926c58" bindtap="__e"><text class="nav-back data-v-0b926c58">‹</text></view><view class="nav-center data-v-0b926c58"><text class="nav-title data-v-0b926c58">{{groupInfo.name}}</text><text class="nav-subtitle data-v-0b926c58">{{groupInfo.memberCount+"人"}}</text></view><view data-event-opts="{{[['tap',[['showGroupInfo',['$event']]]]]}}" class="nav-right data-v-0b926c58" bindtap="__e"><text class="nav-more data-v-0b926c58">⋯</text></view></view><scroll-view class="message-list data-v-0b926c58" scroll-y="{{true}}" scroll-top="{{scrollTop}}" scroll-with-animation="{{true}}" data-event-opts="{{[['scrolltoupper',[['loadMoreMessages',['$event']]]]]}}" bindscrolltoupper="__e"><block wx:if="{{loading}}"><view class="load-more data-v-0b926c58"><text class="load-text data-v-0b926c58">加载中...</text></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="message-wrapper data-v-0b926c58"><block wx:if="{{item.m0}}"><view class="time-divider data-v-0b926c58"><text class="time-text data-v-0b926c58">{{item.m1}}</text></view></block><view class="{{['message-item','data-v-0b926c58',(item.m2)?'is-self':'']}}"><block wx:if="{{!item.m3}}"><view class="avatar-container data-v-0b926c58"><image class="avatar data-v-0b926c58" src="{{item.m4}}" mode="aspectFill" data-event-opts="{{[['tap',[['showUserProfile',['$0'],[[['messageList','id',item.$orig.id,'userId']]]]]]]}}" bindtap="__e"></image></view></block><view class="message-body data-v-0b926c58"><block wx:if="{{item.m5}}"><view class="nickname data-v-0b926c58">{{''+item.m6+''}}</view></block><view data-event-opts="{{[['longpress',[['showMessageMenu',['$0'],[[['messageList','id',item.$orig.id]]]]]]]}}" class="{{item.m7}}" bindlongpress="__e"><block wx:if="{{item.$orig.msgType==='text'}}"><view class="text-content data-v-0b926c58">{{''+item.$orig.content+''}}</view></block><block wx:else><block wx:if="{{item.$orig.msgType==='image'}}"><view class="image-content data-v-0b926c58"><image class="message-image data-v-0b926c58" src="{{item.$orig.content}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['messageList','id',item.$orig.id,'content']]]]]]]}}" bindtap="__e"></image></view></block><block wx:else><block wx:if="{{item.$orig.msgType==='audio'}}"><view class="voice-content data-v-0b926c58"><view class="{{['voice-icon','data-v-0b926c58',(item.$orig.isPlaying)?'playing':'']}}"><text class="voice-waves data-v-0b926c58">🎵</text></view><text class="voice-duration data-v-0b926c58">{{(item.$orig.duration||'1')+"\""}}</text></view></block><block wx:else><view class="other-content data-v-0b926c58"><text class="other-text data-v-0b926c58">{{item.m8}}</text></view></block></block></block></view><block wx:if="{{item.m9}}"><view class="message-status data-v-0b926c58"><block wx:if="{{item.$orig.status==='sending'}}"><text class="status-text data-v-0b926c58">发送中</text></block><block wx:else><block wx:if="{{item.$orig.status==='failed'}}"><text class="status-text data-v-0b926c58">发送失败</text></block></block></view></block></view><block wx:if="{{item.m10}}"><view class="avatar-container data-v-0b926c58"><image class="avatar data-v-0b926c58" src="{{selfAvatar}}" mode="aspectFill"></image></view></block></view></view></block></scroll-view><view class="input-area data-v-0b926c58"><view class="input-container data-v-0b926c58"><view class="input-tools data-v-0b926c58"><view data-event-opts="{{[['tap',[['handleShowEmojiPanel',['$event']]]]]}}" class="tool-item data-v-0b926c58" bindtap="__e"><text class="tool-icon data-v-0b926c58">😊</text></view></view><view class="input-wrapper data-v-0b926c58"><textarea class="message-input data-v-0b926c58" placeholder="输入消息..." auto-height="{{true}}" max-height="{{120}}" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['confirm',[['sendMessage',['$event']]]],['input',[['__set_model',['','inputText','$event',[]]]]]]}}" value="{{inputText}}" bindfocus="__e" bindblur="__e" bindconfirm="__e" bindinput="__e"></textarea></view><view class="input-actions data-v-0b926c58"><block wx:if="{{!$root.g0}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="action-item data-v-0b926c58" bindtap="__e"><text class="action-icon data-v-0b926c58">📷</text></view></block><block wx:if="{{!$root.g1}}"><view data-event-opts="{{[['tap',[['recordVoice',['$event']]]]]}}" class="action-item data-v-0b926c58" bindtap="__e"><text class="action-icon data-v-0b926c58">🎤</text></view></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="send-btn data-v-0b926c58" bindtap="__e"><text class="send-text data-v-0b926c58">发送</text></view></block></view></view></view></view>