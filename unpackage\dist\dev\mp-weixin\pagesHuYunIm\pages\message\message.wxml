<view class="chat-page data-v-0b926c58"><view class="nav-bar data-v-0b926c58"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-0b926c58" bindtap="__e"><text class="nav-back data-v-0b926c58">‹</text></view><view class="nav-center data-v-0b926c58"><text class="nav-title data-v-0b926c58">{{groupInfo.name}}</text><text class="nav-subtitle data-v-0b926c58">{{groupInfo.memberCount+"人"}}</text></view><view data-event-opts="{{[['tap',[['showGroupInfo',['$event']]]]]}}" class="nav-right data-v-0b926c58" bindtap="__e"><text class="nav-more data-v-0b926c58">⋯</text></view></view><scroll-view class="message-list data-v-0b926c58" scroll-y="{{true}}" scroll-top="{{scrollTop}}" scroll-with-animation="{{true}}" data-event-opts="{{[['scrolltoupper',[['loadMoreMessages',['$event']]]]]}}" bindscrolltoupper="__e"><block wx:if="{{loading}}"><view class="load-more data-v-0b926c58"><text class="load-text data-v-0b926c58">加载中...</text></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="simple-message data-v-0b926c58" style="background:white;margin:10rpx;padding:20rpx;border-radius:8rpx;border:1px solid #ddd;"><view style="display:flex;align-items:center;margin-bottom:10rpx;" class="data-v-0b926c58"><text style="font-weight:bold;color:#333;" class="data-v-0b926c58">{{item.m0}}</text><text style="margin-left:10rpx;font-size:24rpx;color:#999;" class="data-v-0b926c58">{{item.$orig.createTime}}</text></view><view style="color:#666;line-height:1.4;" class="data-v-0b926c58">{{''+item.$orig.content+''}}</view></view></block></scroll-view><view class="input-area data-v-0b926c58"><view class="input-container data-v-0b926c58"><view class="input-tools data-v-0b926c58"><view data-event-opts="{{[['tap',[['handleShowEmojiPanel',['$event']]]]]}}" class="tool-item data-v-0b926c58" bindtap="__e"><text class="tool-icon data-v-0b926c58">😊</text></view></view><view class="input-wrapper data-v-0b926c58"><textarea class="message-input data-v-0b926c58" placeholder="输入消息..." auto-height="{{true}}" max-height="{{120}}" data-event-opts="{{[['focus',[['onInputFocus',['$event']]]],['blur',[['onInputBlur',['$event']]]],['confirm',[['sendMessage',['$event']]]],['input',[['__set_model',['','inputText','$event',[]]]]]]}}" value="{{inputText}}" bindfocus="__e" bindblur="__e" bindconfirm="__e" bindinput="__e"></textarea></view><view class="input-actions data-v-0b926c58"><block wx:if="{{!$root.g0}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="action-item data-v-0b926c58" bindtap="__e"><text class="action-icon data-v-0b926c58">📷</text></view></block><block wx:if="{{!$root.g1}}"><view data-event-opts="{{[['tap',[['recordVoice',['$event']]]]]}}" class="action-item data-v-0b926c58" bindtap="__e"><text class="action-icon data-v-0b926c58">🎤</text></view></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['sendMessage',['$event']]]]]}}" class="send-btn data-v-0b926c58" bindtap="__e"><text class="send-text data-v-0b926c58">发送</text></view></block></view></view></view></view>