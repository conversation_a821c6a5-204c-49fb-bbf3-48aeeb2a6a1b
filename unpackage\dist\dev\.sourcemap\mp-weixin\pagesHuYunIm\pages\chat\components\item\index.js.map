{"version": 3, "sources": [null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?bd71", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?ee6b", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?6ab7", "uni-app:///pagesHuYunIm/pages/chat/components/item/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?0131", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/index.vue?7a6a"], "names": ["components", "mText", "mImage", "mVideo", "mAudio", "mRedPacket", "mMap", "mEmojiImg", "mArticle", "mShareSbcf", "mShareMall", "mFunctionalModule", "props", "myid", "type", "default", "isMy", "item", "index", "data", "userInforData", "created", "methods", "imgLoad", "onClick", "longpressAvatar", "isLongpressAvatar", "onItem", "console", "member_id", "group_id", "longpress", "isLongpress", "query", "select", "boundingClientRect", "exec"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACuF5wB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA;AACA;AAAA,eACA;EACAA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACAC;MACA;IACA;IACAC;MACA;MACAC;MACA;QAAAC;QAAAC;MAAA;IACA;IAEA;IACAC;MAAA;MACAC;MACA;MACAJ;MACA;QACA;QACA;QACA;QACA;;QAEA;QACAK,MACAC,0CACAC;UACA;QACA,GACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtLA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=52f9fea2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52f9fea2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=52f9fea2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <!-- 群公告 -->\n    <view class=\"text_26 icon_ group_notice\" v-if=\"item.type === 'group_notice'\">\n      管理员设置了新的\n      <text style=\"color: #fe6702; margin: 0 10rpx\">群公告</text>\n      请及时查看\n    </view>\n    <view class=\"text_26 icon_ group_notice\" v-else-if=\"item.type === 'update_group_name'\">\n      管理员修改了群名称为:\"\n      <text style=\"color: #fe6702; margin: 0 10rpx\">{{ item.payload.name }}</text>\n      \"\n    </view>\n    <view class=\"flex_r item\" :class=\"{ item_: isMy }\" v-else>\n      <view class=\"item-img\" @longpress.stop=\"longpressAvatar\" @click.stop=\"onItem\">\n        <!-- <view class=\"item-img-pendant\">\n\t\t\t\t\t<image class=\"img\" :src=\"item.senderData.avatar_pendant\" mode=\"aspectFill\"></image>\n\t\t\t\t</view> -->\n        <view class=\"z_index2 item-img-url\">\n          <image class=\"img\" :src=\"item.senderData.avatar\" mode=\"aspectFill\"></image>\n        </view>\n      </view>\n      <view style=\"width: 22rpx\"></view>\n      <view class=\"item-content\" :class=\"{ item_content: !isMy, item_content2: !item.senderData.name }\">\n        <view class=\"text_24 color__ item-name\" v-if=\"item.senderData.name\">{{ item.senderData.name }}</view>\n        <view class=\"flex_r fa_c item-content-box\" :class=\"{ row_reverse: isMy }\">\n          <view class=\"flex_r fa_c item-content-box-box\" :class=\"'A' + item.timestamp\" @longpress.stop=\"longpress\">\n            <view v-if=\"isMy\">\n              <view class=\"loading\" v-if=\"item.status === 'new' || item.status === 'sending'\">\n                <m-loading-icon iconColor=\"#989898\"></m-loading-icon>\n              </view>\n              <view class=\"loading\" v-if=\"item.status === 'error'\">\n                <image\n                  class=\"img\"\n                  src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMiAzN2MyNjIuMzEgMCA0NzUgMjEyLjY5IDQ3NSA0NzVTNzc0LjMxIDk4NyA1MTIgOTg3IDM3IDc3NC4zMSAzNyA1MTIgMjQ5LjY5IDM3IDUxMiAzN3ptMCA2NjEuMDhjLTI5LjI4IDAtNTMuMDEgMjMuNzMtNTMuMDEgNTMuMDEgMCAyOS4yOCAyMy43MyA1My4wMSA1My4wMSA1My4wMSAyOS4yOCAwIDUzLjAxLTIzLjczIDUzLjAxLTUzLjAxIDAtMjkuMjgtMjMuNzMtNTMuMDEtNTMuMDEtNTMuMDF6bTAtNDc4LjE4Yy0zNy40MyAwLTY3Ljg2IDMwLjQzLTY3Ljg2IDY3Ljk2bDI1LjQ1IDMxOS43OC41OSA2LjEyYzMuMzkgMTkuOTggMjAuODEgMzUuMjMgNDEuODIgMzUuMjMgMjMuMTEgMCA0MS43Ny0xOC40NSA0Mi40MS00MS4zNWwyNS40NS0zMTkuNzgtLjQtNy40MWMtMy42OS0zNC4wNS0zMi41My02MC41NS02Ny40Ni02MC41NXoiIGZpbGw9IiNmNDM0MmYiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTkuNjJlMTNhODF0ejVJRE4iIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\n                  mode=\"aspectFill\"\n                ></image>\n              </view>\n            </view>\n            <view v-if=\"item.type === 'text' || item.type === 'text_quote'\">\n              <m-text :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-text>\n            </view>\n            <view v-else-if=\"item.type === 'image' || item.type === 'image_transmit'\" @tap.stop=\"onClick\">\n              <m-image :isMy=\"isMy\" :value=\"item\" @imgLoad=\"imgLoad\"></m-image>\n            </view>\n            <view v-else-if=\"item.type === 'video'\" @tap.stop=\"onClick\">\n              <m-video :isMy=\"isMy\" :value=\"item\"></m-video>\n            </view>\n            <view v-else-if=\"item.type === 'audio'\">\n              <m-audio :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-audio>\n            </view>\n            <!-- 红包 -->\n            <view v-else-if=\"item.type === 'red_envelope'\">\n              <m-red-packet :isMy=\"isMy\" :myid=\"myid\" :value=\"item\" @onClick=\"onClick\"></m-red-packet>\n            </view>\n            <!-- 表情包 -->\n            <view v-else-if=\"item.type === 'emoji_pack'\" @tap.stop=\"onClick\">\n              <m-emoji-img :isMy=\"isMy\" :value=\"item\"></m-emoji-img>\n            </view>\n            <!-- 位置 -->\n            <view v-else-if=\"item.type === 'map'\">\n              <m-map :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-map>\n            </view>\n            <!-- 文章 -->\n            <view v-else-if=\"item.type === 'article'\">\n              <m-article :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-article>\n            </view>\n            <!-- 商家分享 -->\n            <view v-else-if=\"item.type === 'share_SBCF'\">\n              <m-share-sbcf :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-share-sbcf>\n            </view>\n            <view v-else-if=\"item.type === 'share_mall'\">\n              <m-share-mall :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-share-mall>\n            </view>\n            <view v-else-if=\"item.type === 'functional_module'\">\n              <m-functional-module :isMy=\"isMy\" :value=\"item\" @onClick=\"onClick\"></m-functional-module>\n            </view>\n\n            <view class=\"flex1\"></view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { to, vibrateShort } from '@/utils/index.js'\nimport mText from './m-text.vue'\nimport mImage from './m-image.vue'\nimport mVideo from './m-video.vue'\nimport mAudio from './m-audio.vue'\nimport mRedPacket from './m-redPacket.vue'\nimport mMap from './m-map.vue'\nimport mEmojiImg from './m-emoji-img.vue'\nimport mArticle from './m-article.vue'\nimport mShareSbcf from './m-share-sbcf.vue'\nimport mShareMall from './m-share-mall.vue'\n\nimport mFunctionalModule from './m-functional-module.vue'\n\nlet isLongpress = false\nlet isLongpressAvatar = false\nexport default {\n  components: {\n    mText,\n    mImage,\n    mVideo,\n    mAudio,\n    mRedPacket,\n    mMap,\n    mEmojiImg,\n    mArticle,\n    mShareSbcf,\n    mShareMall,\n    mFunctionalModule\n  },\n  props: {\n    myid: {\n      type: [String, Number],\n      default: null\n    },\n    isMy: {\n      type: [Boolean, Number],\n      default: false\n    },\n    item: {\n      type: Object,\n      default: {}\n    },\n    index: {\n      type: Number,\n      default: 0\n    }\n  },\n  data() {\n    return {\n      userInforData: {}\n    }\n  },\n  created() {},\n  methods: {\n    imgLoad(e) {\n      this.$emit('imgLoad', e)\n    },\n    onClick() {\n      if (isLongpress) return (isLongpress = false)\n      if (this.item.status === 'error') return\n      this.$emit('onClick', this.item, this.index)\n    },\n    // @某人\n    longpressAvatar() {\n      isLongpressAvatar = true\n      this.$emit('mention', this.item, this.index)\n    },\n    onItem() {\n      if (isLongpressAvatar) return (isLongpressAvatar = false)\n      console.log(this.item)\n      to('/pagesGoEasy/group_member_infor/index', { member_id: this.item.senderId, group_id: this.item.groupId })\n    },\n\n    // 长按\n    longpress(e) {\n      isLongpress = true\n      vibrateShort()\n      console.log(this.item)\n      this.$nextTick(() => {\n        // let view = uni.createSelectorQuery().select(`.A${this.item.timestamp}`);\n        // view.boundingClientRect((data) => {\n        // \tthis.$emit('onLongpress', this.item, data);\n        // }).exec();\n\n        const query = uni.createSelectorQuery().in(this)\n        query\n          .select(`.A${this.item.timestamp}`)\n          .boundingClientRect((data) => {\n            this.$emit('onLongpress', this.item, data)\n          })\n          .exec()\n      })\n    }\n  }\n}\n</script>\n\n<style scoped lang=\"scss\">\n.group_notice {\n  width: 100%;\n  height: 80rpx;\n  color: #a3a3a3;\n}\n.row_reverse {\n  flex-direction: row-reverse;\n}\n.item {\n  box-sizing: border-box;\n  padding-bottom: 20rpx;\n  position: relative;\n  z-index: 0;\n  width: calc(100% - 50rpx);\n  margin: 0 auto;\n  .item-name {\n    margin-bottom: 4rpx;\n  }\n}\n.item_ {\n  flex-direction: row-reverse;\n  .item-name {\n    position: relative;\n    top: 0rpx;\n    display: none;\n  }\n}\n.item-img {\n  position: relative;\n  width: 76rpx;\n  height: 76rpx;\n  .item-img-pendant {\n    position: absolute;\n    z-index: 3;\n    top: -2rpx;\n    left: -2rpx;\n    right: -2rpx;\n    bottom: -2rpx;\n  }\n  .item-img-url {\n    width: 76rpx;\n    height: 76rpx;\n    overflow: hidden;\n    border-radius: 6rpx;\n    background-color: #fff;\n  }\n}\n.item-content {\n  width: calc(100% - 164rpx);\n  .item-content-box {\n    position: relative;\n    .item-content-box-box {\n    }\n    .loading {\n      width: 40rpx;\n      height: 40rpx;\n      margin: 0 10rpx;\n    }\n  }\n}\n.item_content {\n  position: relative;\n  top: -10rpx;\n}\n.item_content2 {\n  position: relative;\n  top: 0rpx !important;\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=52f9fea2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754989836229\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}