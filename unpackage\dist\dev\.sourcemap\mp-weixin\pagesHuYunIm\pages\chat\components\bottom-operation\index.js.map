{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?6393", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?88b3", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?15c7", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?53b0", "uni-app:///pagesHuYunIm/pages/chat/components/bottom-operation/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?9137", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?7b9f"], "names": ["components", "emoji", "more", "mRecorder", "memberSelectionLoading", "mText", "mImage", "mAudio", "mOther", "props", "to", "type", "default", "userList", "isPrivate", "data", "a", "a_b", "b", "c", "isFocus", "isKeyboard", "is<PERSON><PERSON><PERSON>", "isMore", "isRecorder", "isCancel", "text", "keyboardHeight", "isQuote", "quoteSource", "keyHeight", "created", "uni", "console", "<PERSON><PERSON><PERSON><PERSON>", "cursor", "clearInterval", "methods", "setCursor", "getSelectedTextRangeSetInterval", "success", "fail", "backToBottom", "closeAll", "onBottom", "recalledEdit", "close", "onKeyboard", "keyboardheightchange", "<PERSON><PERSON><PERSON><PERSON>", "tapMore", "on<PERSON><PERSON><PERSON>", "focus", "handleBlur", "input", "inputValue", "lineBreak", "itemclick", "deleteFn", "del", "xstr", "quote", "itemx", "thank", "mention", "setTimeout", "cancelQuote", "recorderTop", "initRecorderListeners", "recorder<PERSON>anager", "startTime", "res", "touchstart", "touchmove", "touchend", "onMore", "title", "openShot", "plug", "setMaxduration", "SpeedColor", "ratio", "mp4", "duration", "size", "image", "errMsg", "tempFile<PERSON>ath", "width", "height", "path", "sendingText", "showCancel", "content", "body", "payload", "createCustomMessageMap", "longitude", "address", "name", "latitude", "createCustomMessageText", "sendImageMessage", "count", "createImageMessage", "contentType", "url", "thumbnail", "sendVideoMessage", "createVideoMessage", "video", "sendingRecorder", "sendingEmojiPack", "ext", "sendMessage", "groupId", "senderData", "senderId", "messageId", "timestamp", "recalled", "status", "isHide"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACuH5wB;AAEA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAUA;AACA;AACA;;AAEA;AACA;AACA;AAAA,gBACA;EACAA;IACAC;IACAC;IACAC;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACAC;IACAA;;IAEA;IACAA;IACAA;IAGAA;MACAC;MACA;IACA;EAGA;EACAC;IACAF;IACAA;IACAG;IACAC;EACA;EACAC;IACAC;MACAC;QACAP;UACAQ;YACAL;UACA;UACAM;YACAL;UACA;QACA;MACA;IACA;IACA;IACAM;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IAEAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MAIA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;MAgBA;QACA;UACA;UACA;UACA;UACA;QACA;MACA;IAEA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;MAIA;IACA;IACAC;MACA;MACA;MACA;MAIA;IACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MAOA;MACA;MACA;MAEAhB;MACA;IACA;IACAiB;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA,2CACA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;MACA;MACAC;IACA;IACA;IACAC;MAAA;MACAvB;MACAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAwB;MAAA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QAAA,IAQAC;UACA;QACA;QATA;QACA;QACA;UACAC;QACA;QACA;QAKA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;UACA;QACA;MACA;MACA;QACA;MACA;QACA;MAAA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC;QACA;QACAC;MACA;MACA;MACAD;QACA;QACA;QACA;QACA;QACA;QACAE;QACA;QACA;MACA;MACA;MACAF;QACA;QACAA;QACA;MACA;IACA;IACA;IACAG;MACA;MACA;MACA;QACAH;MACA;QACA;MACA;IACA;IACA;IACAI;MACA;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAL;MACA;QACApC;MACA;MACA;IACA;IACA;IACA;IACA0C;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA3C;UACAA;;UAEA;UACA;YACA,iFACA,SACA;UACA;YACA,yEACA,SACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACAA;YACAQ;cAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBACAP;wBACAD;0BACA4C;wBACA;wBACA;0BACA;wBACA;wBACA5C;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CACA;cAAA;gBAAA;cAAA;cAAA;YAAA;YACAS;cACAR;YACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IACA4C;MAAA;MACA;MACA;MAEA;MACAC,UACA;QACAC;QACAC;QACAC;MACA,GACA;QACA;UAAA;UAAAC;UAAA;UAAAC;UAAA;UAAAC;UAAAC;QACA;UACA;YACAC;YACAC;YACAH;YACAD;YAAA;YACAK;YACAC;UACA;UACA;QACA;UACA;YACAL;YACAM;UACA;QACA;QACA;QACA,gCACA;MACA,EACA;IACA;IACA;IACA;IACAC;MACA,sBACA;QACAC;QACAC;QACArD;MACA;MACA;MACA;QACAsD;MACA;MACA;QACA;QACA;MACA;MAEA;QACAC;UACArE;QACA;QACAf;MACA;MAEA;IACA;IAEA;IACAqF;MACA;QAAAC;QAAAC;QAAAC;MACA;QACAJ;UACAK;UACAH;UACArB;UACAsB;UACAb;QACA;;QAEA1E;MACA;IACA;IAEA;IACA0F;MACA;QACAN;UACArE;UACA;UACAG,+BACA;QAEA;QACAlB;MACA;MAEA;IACA;IAEA;IACA2F;MAAA;MACAtE;QACAuE;QACA/D;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACA+B;sBACAtC;sBACA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACAuE;MACA;QACAT;UACAU;UACAN;UACAf;UACAsB;UACAlB;UACAC;UACAkB;QACA;QACAhG;MACA;IACA;IACA;IACAiG;MAAA;MACA5E;QACAQ;UAAA;YAAA;cAAA;gBAAA;kBAAA;oBACAP;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CACA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA4E;MACA;QACAd;UACAe;YACAX;YACAO;YACAlB;YACAC;YACAgB;YACArB;YACAD;UACA;UACAwB;YACAR;YACAO;YACAlB;YACAC;YACAgB;UACA;QACA;QACA9F;MACA;IACA;IACA;IACAoG;MACA;QACAhB;UACAU;UACAN;UACAf;UACAsB;UACAvB;QACA;QACAxE;MACA;IACA;IAEA;IACAqG;MACA;QACAjB;UACAkB;UACAP;UACAhB;UACAhE;QACA;QACAf;MACA;IACA;IAEA;IACAuG;MAAA;QAAAvG;MACA;QACAwG;QACAC;QACAC;QACAC;QACAvB;QACAwB;QACA5G;QACA6G;QACAC;QACAC;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACnuBA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/bottom-operation/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0c5cade1&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c5cade1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/bottom-operation/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0c5cade1&scoped=true&\"", "var components\ntry {\n  components = {\n    mBottomPaceholder: function () {\n      return import(\n        /* webpackChunkName: \"components/m-bottom-paceholder/m-bottom-paceholder\" */ \"@/components/m-bottom-paceholder/m-bottom-paceholder.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.text.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"bottom-operation-box\" @tap.stop=\"onBottom\">\r\n\t\t\t<!-- <view class=\"flex_r line-break\" v-if=\"keyboardHeight\"> -->\r\n\t\t\t<view class=\"flex_r line-break\" v-show=\"keyboardHeight\">\r\n\t\t\t\t<view class=\"icon_ text_28 color__ line-break-box\" @click=\"lineBreak\">\r\n\t\t\t\t\t<view class=\"icon_ line-break-icon\">\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTAgMGgxMDI0djEwMjRIMHoiIGZpbGw9IiMyYzJjMmMiIGZpbGwtb3BhY2l0eT0iLjAxIi8+PHBhdGggZD0iTTY4Mi42NjcgMTI4YTI5OC42NjcgMjk4LjY2NyAwIDAgMSAxMC4yNCA1OTcuMTYzbC0xMC4yNC4xN2gtNTEyYTQyLjY2NyA0Mi42NjcgMCAwIDEtNC45OTItODUuMDM0bDQuOTkyLS4yOTloNTEyYTIxMy4zMzMgMjEzLjMzMyAwIDAgMCA5LjI1OC00MjYuNDUzbC05LjI1OC0uMjE0aC01MTJhNDIuNjY3IDQyLjY2NyAwIDAgMS00Ljk5Mi04NS4wMzRsNC45OTItLjI5OWg1MTJ6IiBmaWxsPSIjMmMyYzJjIi8+PHBhdGggZD0iTTI0Ny4xNjggNDYwLjUwMWE0Mi42NjcgNDIuNjY3IDAgMCAxIDYzLjg3MiA1Ni4zMmwtMy41NDEgNC4wMTEtMTYwIDE2MCAxNTguMTY1IDE0MC42M2E0Mi42NjcgNDIuNjY3IDAgMCAxIDYuODI3IDU1Ljk3OGwtMy4yODYgNC4yNjdhNDIuNjY3IDQyLjY2NyAwIDAgMS01NS45NzggNi44MjZsLTQuMjY3LTMuMzI4LTE5Mi0xNzAuNjY2YTQyLjY2NyA0Mi42NjcgMCAwIDEtNS4yNDgtNTguMTU1bDMuNDEzLTMuODgzIDE5Mi0xOTJ6IiBmaWxsPSIjMmMyYzJjIi8+PC9zdmc+\"\r\n\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t换行\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"flex_r bottom-operation\">\r\n\t\t\t\t<view class=\"icon_ bottom-operation-icon\" @click=\"onKeyboard\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"isKeyboard ? a : a_b\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 10rpx\"></view>\r\n\t\t\t\t<view class=\"flex_c_c flex1\">\r\n\t\t\t\t\t<view class=\"bottom-operation-input\" v-if=\"isKeyboard\">\r\n\t\t\t\t\t\t<textarea\r\n\t\t\t\t\t\t\tclass=\"input\"\r\n\t\t\t\t\t\t\tauto-height=\"true\"\r\n\t\t\t\t\t\t\tconfirm-type=\"send\"\r\n\t\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\t\t:focus=\"isFocus\"\r\n\t\t\t\t\t\t\t:maxlength=\"-1\"\r\n\t\t\t\t\t\t\t:adjust-position=\"false\"\r\n\t\t\t\t\t\t\tv-model=\"text\"\r\n\t\t\t\t\t\t\tconfirm-hold\r\n\t\t\t\t\t\t\t:show-confirm-bar=\"false\"\r\n\t\t\t\t\t\t\t@input=\"input\"\r\n\t\t\t\t\t\t\t@confirm=\"sendingText\"\r\n\t\t\t\t\t\t\t@focus=\"focus\"\r\n\t\t\t\t\t\t\t@blur=\"handleBlur\"\r\n\t\t\t\t\t\t\t@keyboardheightchange=\"keyboardheightchange\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- inputmode=\"none\" -->\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"icon_ text_32 bold_ bottom-operation-input\"\r\n\t\t\t\t\t\t@touchend=\"touchend\"\r\n\t\t\t\t\t\t@touchmove=\"touchmove\"\r\n\t\t\t\t\t\t@touchstart=\"touchstart\"\r\n\t\t\t\t\t\tv-else\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view>按住</view>\r\n\t\t\t\t\t\t<view style=\"width: 10rpx\"></view>\r\n\t\t\t\t\t\t<view>说话</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"icon_ text_26 quote\" v-if=\"isQuote\">\r\n\t\t\t\t\t\t<view class=\"flex1 quote-row\">\r\n\t\t\t\t\t\t\t<view class=\"\" v-if=\"quoteSource.type === 'image' || quoteSource.type === 'image_transmit'\">\r\n\t\t\t\t\t\t\t\t<m-image :value=\"quoteSource\"></m-image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"\" v-else-if=\"quoteSource.type === 'audio'\">\r\n\t\t\t\t\t\t\t\t<m-audio :value=\"quoteSource\"></m-audio>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"\" v-else-if=\"quoteSource.type === 'text' || quoteSource.type === 'text_quote'\">\r\n\t\t\t\t\t\t\t\t<m-text :value=\"quoteSource\"></m-text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"\" v-else>\r\n\t\t\t\t\t\t\t\t<m-other :value=\"quoteSource\"></m-other>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"quote-icon\" @click=\"cancelQuote\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\tclass=\"img\"\r\n\t\t\t\t\t\t\t\tsrc=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTg5NS4xIDUxMi40Yy0uNi0xMDIuMy00MS0xOTguNC0xMTMuOC0yNzAuNy03Mi44LTcyLjMtMTY5LjItMTEyLTI3MS41LTExMS45LTEwMi4zLjItMTk4LjMgNDAuMi0yNzAuMyAxMTIuN1MxMjguMiA0MTEuMyAxMjguOCA1MTMuNmMuNiAxMDIuMyA0MSAxOTguNCAxMTMuOCAyNzAuN3MxNjkuMiAxMTIgMjcxLjUgMTExLjhjMTAyLjQtLjEgMTk4LjUtNDAuMSAyNzAuNC0xMTIuNiA3Mi03Mi41IDExMS4zLTE2OC43IDExMC42LTI3MS4xek02MjkgNjY3LjhsLTExNi44LTExNi0xMTYgMTE2LjhjLTEwLjcgMTAuOC0yOCAxMC44LTM4LjguMS0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44bDExNS45LTExNi44LTExNi44LTExNS45Yy0xMC43LTEwLjctMTAuOC0yOC0uMS0zOC44IDEwLjctMTAuNyAyOC0xMC44IDM4LjgtLjFsMTE2LjggMTE1LjkgMTE1LjktMTE2LjhjMTAuNy0xMC43IDI4LTEwLjggMzguOC0uMSAxMC43IDEwLjcgMTAuOCAyOCAuMSAzOC44TDU1MC44IDUxMi45bDExNi44IDExNS45YzEwLjggMTAuNyAxMC44IDI4IC4xIDM4LjgtMTAuNiAxMC44LTI4IDEwLjgtMzguNy4yem0wIDAiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTExLjFkYjQzYTgxVE1Sd1EyIiBjbGFzcz0ic2VsZWN0ZWQiIGZpbGw9IiNhMWExYTEiLz48L3N2Zz4=\"\r\n\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"width: 10rpx\"></view>\r\n\t\t\t\t<view class=\"icon_ bottom-operation-icon\" @click=\"tapEmoji\">\r\n\t\t\t\t\t<image class=\"img\" :src=\"b\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"icon_ bottom-operation-icon\" v-if=\"text.length\" @click=\"sendingText\">\r\n\t\t\t\t\t<button class=\"send-btn\">发送</button>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"icon_ bottom-operation-icon\" @click=\"tapMore\" v-else>\r\n\t\t\t\t\t<image class=\"img\" :src=\"c\" mode=\"aspectFill\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view>\r\n\t\t\t\t<emoji\r\n\t\t\t\t\tv-model=\"isEmoji\"\r\n\t\t\t\t\t@onEmoji=\"onEmoji\"\r\n\t\t\t\t\t@deleteFn=\"deleteFn\"\r\n\t\t\t\t\t@sendingText=\"sendingText\"\r\n\t\t\t\t\t@sendingEmojiPack=\"sendingEmojiPack\"\r\n\t\t\t\t></emoji>\r\n\t\t\t</view>\r\n\t\t\t<view>\r\n\t\t\t\t<more v-model=\"isMore\" @onMore=\"onMore\"></more>\r\n\t\t\t</view>\r\n\t\t\t<!-- 键盘高度 -->\r\n\t\t\t<view class=\"keyboard\" :style=\"{ height: keyboardHeight + 'px' }\"></view>\r\n\t\t\t<view v-if=\"keyboardHeight === 0\">\r\n\t\t\t\t<m-bottom-paceholder></m-bottom-paceholder>\r\n\t\t\t</view>\r\n\t\t\t<!-- 语音输入 -->\r\n\t\t\t<m-recorder v-model=\"isRecorder\" :isCancel=\"isCancel\" @recorderTop=\"recorderTop\" @touchend=\"touchend\"></m-recorder>\r\n\t\t</view>\r\n\t\t<member-selection-loading\r\n\t\t\ttitle=\"选择提醒的人\"\r\n\t\t\tref=\"memberSelectionLoadingRef\"\r\n\t\t\t:group_id=\"to.id\"\r\n\t\t\t@itemclick=\"itemclick\"\r\n\t\t></member-selection-loading>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { 自己的信息 } from '@/TEST/index'\r\n\r\nimport { show, to as tofn, throttle, vibrateShortFn } from '@/utils/index.js'\r\nimport emoji from './emoji.vue'\r\nimport more from './more.vue'\r\nimport mRecorder from './m-recorder.vue'\r\nimport memberSelectionLoading from '../../../../components/memberSelection/index.vue'\r\nimport mText from '../item/quoteType/m-text.vue'\r\nimport mImage from '../item/quoteType/m-image.vue'\r\nimport mAudio from '../item/quoteType/m-audio.vue'\r\nimport mOther from '../item/quoteType/m-other.vue'\r\n\r\nconst recorderManager = uni.getRecorderManager()\r\n//录音时长\r\nlet startTime = 0\r\n//\r\nlet inputValue = ''\r\nlet getSelectedTextRangeSetInterval = null\r\nlet cursor = 0 //输入框光标\r\nexport default {\r\n\tcomponents: {\r\n\t\temoji,\r\n\t\tmore,\r\n\t\tmRecorder,\r\n\t\t// memberSelection,\r\n\t\tmemberSelectionLoading,\r\n\t\tmText,\r\n\t\tmImage,\r\n\t\tmAudio,\r\n\t\tmOther\r\n\t},\r\n\tprops: {\r\n\t\tto: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault: {}\r\n\t\t},\r\n\t\tuserList: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: () => {\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t},\r\n\t\tisPrivate: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\ta: 'data:image/svg+xml;base64,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',\r\n\t\t\ta_b: 'data:image/svg+xml;base64,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',\r\n\t\t\tb: 'data:image/svg+xml;base64,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',\r\n\t\t\tc: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS4zIDk1OS43Yy02MC40IDAtMTE5LTExLjgtMTc0LjItMzUuMi01My4zLTIyLjUtMTAxLjEtNTQuOC0xNDIuMi05NS45LTQxLjEtNDEuMS03My40LTg4LjktOTUuOS0xNDIuMi0yMy4zLTU1LjItMzUuMi0xMTMuOC0zNS4yLTE3NC4yUzc1LjYgMzkzLjEgOTkgMzM4YzIyLjUtNTMuMyA1NC44LTEwMS4xIDk1LjktMTQyLjIgNDEuMS00MS4xIDg4LjktNzMuNCAxNDIuMi05NS45IDU1LjItMjMuMyAxMTMuOC0zNS4yIDE3NC4yLTM1LjIgNjAuNCAwIDExOSAxMS44IDE3NC4yIDM1LjIgNTMuMyAyMi41IDEwMS4xIDU0LjggMTQyLjIgOTUuOSA0MS4xIDQxLjEgNzMuNCA4OC45IDk1LjkgMTQyLjIgMjMuMyA1NS4yIDM1LjIgMTEzLjggMzUuMiAxNzQuMnMtMTEuOCAxMTktMzUuMiAxNzQuMmMtMjIuNSA1My4zLTU0LjggMTAxLjEtOTUuOSAxNDIuMi00MS4xIDQxLjEtODguOSA3My40LTE0Mi4yIDk1LjktNTUuMiAyMy4zLTExMy44IDM1LjItMTc0LjIgMzUuMnptMC04MzljLTUyLjkgMC0xMDQuMSAxMC4zLTE1Mi40IDMwLjgtNDYuNiAxOS43LTg4LjUgNDcuOS0xMjQuNSA4My45LTM2IDM2LTY0LjIgNzcuOC04My45IDEyNC41LTIwLjQgNDguMi0zMC44IDk5LjUtMzAuOCAxNTIuNHMxMC4zIDEwNC4xIDMwLjggMTUyLjRjMTkuNyA0Ni42IDQ3LjkgODguNSA4My45IDEyNC41IDM2IDM2IDc3LjggNjQuMiAxMjQuNSA4My45IDQ4LjIgMjAuNCA5OS41IDMwLjggMTUyLjQgMzAuOCA1Mi45IDAgMTA0LjEtMTAuMyAxNTIuNC0zMC44IDQ2LjYtMTkuNyA4OC41LTQ3LjkgMTI0LjUtODMuOXM2NC4yLTc3LjggODMuOS0xMjQuNWMyMC40LTQ4LjIgMzAuOC05OS41IDMwLjgtMTUyLjRTODkyLjQgNDA4IDg3MiAzNTkuOGMtMTkuNy00Ni42LTQ3LjktODguNS04My45LTEyNC41cy03Ny44LTY0LjItMTI0LjUtODMuOWMtNDguMi0yMC40LTk5LjUtMzAuNy0xNTIuMy0zMC43eiIvPjxwYXRoIGQ9Ik03MzcuMyA0ODQuMmgtMTk4di0xOThjMC0xNS41LTEyLjUtMjgtMjgtMjhzLTI4IDEyLjUtMjggMjh2MTk4aC0xOThjLTE1LjUgMC0yOCAxMi41LTI4IDI4czEyLjUgMjggMjggMjhoMTk4djE5OGMwIDE1LjUgMTIuNSAyOCAyOCAyOHMyOC0xMi41IDI4LTI4di0xOThoMTk4YzE1LjUgMCAyOC0xMi41IDI4LTI4cy0xMi42LTI4LTI4LTI4eiIvPjwvc3ZnPg==',\r\n\t\t\tisFocus: false, //键盘焦点\r\n\t\t\tisKeyboard: true,\r\n\t\t\tisEmoji: false,\r\n\t\t\tisMore: false,\r\n\t\t\tisRecorder: false,\r\n\t\t\tisCancel: false, //是否滑动到取消\r\n\t\t\ttext: '',\r\n\t\t\tkeyboardHeight: 0,\r\n\t\t\tisQuote: false, //是否引用\r\n\t\t\tquoteSource: {}, //引用的源\r\n\t\t\tkeyHeight: 0\r\n\t\t}\r\n\t},\r\n\tcreated() {\r\n\t\tthis.initRecorderListeners()\r\n\t\t// 监听设置群公告\r\n\t\tuni.$off('getNoticeSendMessage', this.sendMessage)\r\n\t\tuni.$on('getNoticeSendMessage', this.sendMessage)\r\n\r\n\t\t// 监听修改群明\r\n\t\tuni.$off('getGroupNameMessage', this.sendMessage)\r\n\t\tuni.$on('getGroupNameMessage', this.sendMessage)\r\n\r\n\t\t// #ifdef MP-WEIXIN\r\n\t\tuni.onKeyboardHeightChange((res) => {\r\n\t\t\tconsole.log('res.height', res.height)\r\n\t\t\tthis.keyHeight = res.height\r\n\t\t})\r\n\r\n\t\t// #endif\r\n\t},\r\n\tbeforeDestroy() {\r\n\t\tuni.$off('getNoticeSendMessage', this.sendMessage)\r\n\t\tuni.$off('getGroupNameMessage', this.sendMessage)\r\n\t\tcursor = 0\r\n\t\tclearInterval(getSelectedTextRangeSetInterval)\r\n\t},\r\n\tmethods: {\r\n\t\tsetCursor() {\r\n\t\t\tgetSelectedTextRangeSetInterval = setInterval(() => {\r\n\t\t\t\tuni.getSelectedTextRange({\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tcursor = res.start\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tclearInterval(getSelectedTextRangeSetInterval)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}, 800)\r\n\t\t},\r\n\t\t// 滚动到底部\r\n\t\tbackToBottom() {\r\n\t\t\tthis.$emit('backToBottom')\r\n\t\t},\r\n\t\t// 关闭全部弹出/输入框/表情包\r\n\t\tcloseAll() {\r\n\t\t\tthis.isMore = false\r\n\t\t\tthis.isEmoji = false\r\n\t\t\tthis.isFocus = false\r\n\t\t},\r\n\r\n\t\tonBottom() {\r\n\t\t\tthis.$emit('onBottom')\r\n\t\t},\r\n\t\t// 重新编辑\r\n\t\trecalledEdit(item) {\r\n\t\t\tthis.text = item.payload.text\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isFocus = true\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 关闭\r\n\t\tclose() {\r\n\t\t\tthis.isMore = false\r\n\t\t\tthis.isEmoji = false\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.keyboardHeight = 0\r\n\t\t\t// #endif\r\n\t\t\tthis.$emit('keyboardheightchange', 0)\r\n\t\t},\r\n\t\t// 切换语音输入\r\n\t\tonKeyboard() {\r\n\t\t\tthis.isKeyboard = !this.isKeyboard\r\n\t\t\tthis.isMore = false\r\n\t\t\tthis.isEmoji = false\r\n\t\t},\r\n\t\tkeyboardheightchange(e) {\r\n\t\t\tif (e.detail.duration > 0) {\r\n\t\t\t\tthis.backToBottom()\r\n\t\t\t}\r\n\t\t\t// #ifdef APP || H5\r\n\t\t\tlet height = e.detail.height\r\n\t\t\tthis.keyboardHeight = height\r\n\t\t\tif (height > 0) {\r\n\t\t\t\tconst res = uni.getSystemInfoSync()\r\n\t\t\t\tlet bottom = res.safeAreaInsets.bottom\r\n\t\t\t\theight -= bottom\r\n\t\t\t\theight -= uni.upx2px(20) //补偿高度\r\n\t\t\t}\r\n\t\t\tthis.isMore = false\r\n\t\t\tthis.isEmoji = false\r\n\t\t\tthis.$emit('keyboardheightchange', height, true)\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef MP\r\n\t\t\tif (e.detail.duration > 0) {\r\n\t\t\t\tthrottle(() => {\r\n\t\t\t\t\tthis.keyboardHeight = e.detail.height\r\n\t\t\t\t\tthis.isMore = false\r\n\t\t\t\t\tthis.isEmoji = false\r\n\t\t\t\t\tthis.$emit('keyboardheightchange', this.keyboardHeight)\r\n\t\t\t\t}, 300)\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\ttapEmoji() {\r\n\t\t\tthis.backToBottom()\r\n\t\t\tthis.isEmoji = !this.isEmoji\r\n\t\t\tif (this.isEmoji) {\r\n\t\t\t\tthis.isKeyboard = true\r\n\t\t\t}\r\n\t\t\tthis.isMore = false\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.keyboardHeight = 0\r\n\t\t\t// #endif\r\n\t\t\tthis.$emit('keyboardheightchange', uni.upx2px(690))\r\n\t\t},\r\n\t\ttapMore() {\r\n\t\t\tthis.backToBottom()\r\n\t\t\tthis.isMore = !this.isMore\r\n\t\t\tthis.isEmoji = false\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.keyboardHeight = 0\r\n\t\t\t// #endif\r\n\t\t\tthis.$emit('keyboardheightchange', uni.upx2px(430))\r\n\t\t},\r\n\t\tonEmoji(key) {\r\n\t\t\tconst text = `${this.text.slice(0, cursor)}${key}${this.text.slice(cursor)}`\r\n\t\t\tthis.text = text\r\n\t\t},\r\n\t\t// ===========================\r\n\t\t// 获取焦点\r\n\t\tfocus(e) {\r\n\t\t\tthis.$emit('focus')\r\n\t\t\tthis.isFocus = true\r\n\t\t\tthis.isEmoji = false\r\n\t\t\tthis.isMore = false\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.keyboardHeight = 300\r\n\t\t\tthis.$emit('keyboardheightchange', this.keyboardHeight)\r\n\t\t\tthis.backToBottom()\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.keyboardHeight = this.keyHeight\r\n\t\t\tthis.$emit('keyboardheightchange', this.keyboardHeight)\r\n\t\t\tthis.backToBottom()\r\n\t\t\t// #endif\r\n\t\t\tclearInterval(getSelectedTextRangeSetInterval)\r\n\t\t\tthis.setCursor()\r\n\t\t},\r\n\t\thandleBlur() {\r\n\t\t\tthis.isFocus = false\r\n\t\t\tthis.keyboardHeight = 0\r\n\t\t\tthis.$emit('keyboardheightchange', this.keyboardHeight)\r\n\t\t\tthis.backToBottom()\r\n\t\t},\r\n\t\t// 监听输入,\r\n\t\tinput() {\r\n\t\t\tif (inputValue.length > this.text.length) {\r\n\t\t\t} else {\r\n\t\t\t\tconst str = this.text.charAt(this.text.length - 1)\r\n\t\t\t\tif (str === '@') {\r\n\t\t\t\t\tif (this.isFocus === false) return\r\n\t\t\t\t\tthis.$refs.memberSelectionLoadingRef.open()\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.isFocus = false\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tinputValue = this.text\r\n\t\t},\r\n\t\t// 插入换行符合\r\n\t\tlineBreak() {\r\n\t\t\tconsole.log('回车')\r\n\t\t\tconsole.log(cursor)\r\n\t\t\tconst text = `${this.text.slice(0, cursor)}\\n${this.text.slice(cursor)}`\r\n\t\t\tthis.text = text\r\n\t\t\t// this.text = `${this.text}\\r\\n`;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isFocus = true\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 输入@某个成员\r\n\t\titemclick(item) {\r\n\t\t\tif (item) {\r\n\t\t\t\tthis.text = `${this.text}${item.name} `\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isFocus = true\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 删除表情\r\n\t\tdeleteFn() {\r\n\t\t\tconst str = this.text.charAt(this.text.length - 1)\r\n\t\t\tif (str === ']') {\r\n\t\t\t\tlet metaChars = /\\[.*?(\\u4e00*\\u597d*)\\]/g\r\n\t\t\t\tlet xstr = ''\r\n\t\t\t\tthis.text.replace(metaChars, (match) => {\r\n\t\t\t\t\txstr = match\r\n\t\t\t\t})\r\n\t\t\t\tvar text = this.text\r\n\r\n\t\t\t\tfunction del(str) {\r\n\t\t\t\t\treturn text.slice(0, text.length - str.length)\r\n\t\t\t\t}\r\n\t\t\t\tthis.text = del(xstr)\r\n\t\t\t} else {\r\n\t\t\t\tthis.text = this.text.substring(0, this.text.length - 1)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 引用\r\n\t\tquote(item) {\r\n\t\t\t// 删除嵌套引用\r\n\t\t\tconst itemx = JSON.parse(JSON.stringify(item))\r\n\t\t\titemx.payload['quoteSource'] = {}\r\n\t\t\tthis.isQuote = true\r\n\t\t\tthis.quoteSource = itemx\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.isFocus = true\r\n\t\t\t})\r\n\t\t},\r\n\t\t//谢谢红包\r\n\t\tthank(item) {\r\n\t\t\tthis.text = '[彩带][玫瑰]谢谢红包！'\r\n\t\t\tthis.sendingText()\r\n\t\t},\r\n\t\t//长按@某人\r\n\t\tmention(item) {\r\n\t\t\tthis.text = `${this.text}@${item.senderData.name} `\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.isFocus = true\r\n\t\t\t\t}, 500)\r\n\t\t\t})\r\n\t\t\ttry {\r\n\t\t\t\tvibrateShortFn()\r\n\t\t\t} catch (e) {\r\n\t\t\t\t//TODO handle the exception\r\n\t\t\t}\r\n\t\t},\r\n\t\tcancelQuote() {\r\n\t\t\tthis.isQuote = false\r\n\t\t},\r\n\r\n\t\t// 录音相关===============\r\n\t\trecorderTop(e) {\r\n\t\t\tthis.recorderTopValue = e?.top\r\n\t\t},\r\n\t\tinitRecorderListeners() {\r\n\t\t\t// 监听录音开始\r\n\t\t\trecorderManager.onStart(() => {\r\n\t\t\t\t// console.log('开始录音');\r\n\t\t\t\tstartTime = Date.now()\r\n\t\t\t})\r\n\t\t\t//录音结束后，发送\r\n\t\t\trecorderManager.onStop((res) => {\r\n\t\t\t\tthis.isRecorder = false\r\n\t\t\t\tif (this.isCancel) return console.log('取消发送') //取消发送\r\n\t\t\t\tlet endTime = Date.now()\r\n\t\t\t\tlet duration = endTime - startTime\r\n\t\t\t\tif (duration < 1000) return show('录音时间太短', 1000, 'error')\r\n\t\t\t\tres.duration = duration\r\n\t\t\t\t// 创建信息\r\n\t\t\t\tthis.sendingRecorder(res)\r\n\t\t\t})\r\n\t\t\t// 监听录音报错\r\n\t\t\trecorderManager.onError((res) => {\r\n\t\t\t\tthis.isRecorder = false\r\n\t\t\t\trecorderManager.stop()\r\n\t\t\t\tshow('请检查麦克风权限')\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 按下\r\n\t\ttouchstart() {\r\n\t\t\tthis.isRecorder = true\r\n\t\t\tthis.isCancel = false\r\n\t\t\ttry {\r\n\t\t\t\trecorderManager.start()\r\n\t\t\t} catch (e) {\r\n\t\t\t\tshow('H5不支持')\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 拖拽中\r\n\t\ttouchmove(e) {\r\n\t\t\tlet touch = e.touches[0] //滑动过程中，手指滑动的坐标信息 返回的是Objcet对象\r\n\t\t\tif (touch.clientY <= this.recorderTopValue) {\r\n\t\t\t\t// 取消发送\r\n\t\t\t\tthis.isCancel = true\r\n\t\t\t} else {\r\n\t\t\t\tthis.isCancel = false\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 松手\r\n\t\ttouchend() {\r\n\t\t\ttry {\r\n\t\t\t\trecorderManager.stop()\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.log('e:', e)\r\n\t\t\t}\r\n\t\t\tthis.isRecorder = false\r\n\t\t},\r\n\t\t// ===================\r\n\t\t// 更多操作相关===============\r\n\t\tonMore(item) {\r\n\t\t\tswitch (item.type) {\r\n\t\t\t\t// 拍摄\r\n\t\t\t\tcase 'shot':\r\n\t\t\t\t\tthis.openShot()\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'img':\r\n\t\t\t\t\tthis.sendImageMessage()\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'video':\r\n\t\t\t\t\tthis.sendVideoMessage()\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'red_envelope':\r\n\t\t\t\t\t// 发红包\r\n\t\t\t\t\tuni.$off('send_red_envelope', this.sendMessage)\r\n\t\t\t\t\tuni.$on('send_red_envelope', this.sendMessage)\r\n\r\n\t\t\t\t\t// 是否是单聊\r\n\t\t\t\t\tif (this.isPrivate) {\r\n\t\t\t\t\t\ttofn('/pagesGoEasy/envelope_sending/index-private', {\r\n\t\t\t\t\t\t\t...this.to\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\ttofn('/pagesGoEasy/envelope_sending/index', {\r\n\t\t\t\t\t\t\t...this.to\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'mutualism':\r\n\t\t\t\t\t// 蝌蚪互转\r\n\t\t\t\t\ttofn('/pagesThree/tadpoleChange/index?type=1')\r\n\t\t\t\t\tbreak\r\n\t\t\t\tcase 'map':\r\n\t\t\t\t\t// 位置\r\n\t\t\t\t\tuni.chooseLocation({\r\n\t\t\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\t\ttitle: '发送中'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\tif (res2) {\r\n\t\t\t\t\t\t\t\tthis.createCustomMessageMap(res, 'http://xxxxxxxx/map/staticMap?location=116.459044,39.918732&size=300*170')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(e) {\r\n\t\t\t\t\t\t\tconsole.log(e)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tbreak\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tbreak\r\n\t\t\t}\r\n\t\t},\r\n\t\topenShot() {\r\n\t\t\tshow('这个用的是原生插件，Html5App-CameraView')\r\n\t\t\treturn\r\n\r\n\t\t\tconst plug = uni.requireNativePlugin('Html5App-CameraView')\r\n\t\t\tplug.open(\r\n\t\t\t\t{\r\n\t\t\t\t\tsetMaxduration: 30,\r\n\t\t\t\t\tSpeedColor: '#05c160',\r\n\t\t\t\t\tratio: '9/16'\r\n\t\t\t\t},\r\n\t\t\t\t(retult) => {\r\n\t\t\t\t\tconst { type, mp4 = '', duration = '', size = '', image } = retult\r\n\t\t\t\t\tif (type == 'video') {\r\n\t\t\t\t\t\tconst file = {\r\n\t\t\t\t\t\t\terrMsg: 'chooseVideo:ok',\r\n\t\t\t\t\t\t\ttempFilePath: mp4,\r\n\t\t\t\t\t\t\tsize: Number(size) * 1000,\r\n\t\t\t\t\t\t\tduration: duration, //视频时间\r\n\t\t\t\t\t\t\twidth: 360,\r\n\t\t\t\t\t\t\theight: 640\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.createVideoMessage(file)\r\n\t\t\t\t\t} else if (type == 'image') {\r\n\t\t\t\t\t\tthis.createImageMessage({\r\n\t\t\t\t\t\t\tsize: Number(size) * 1000,\r\n\t\t\t\t\t\t\tpath: image\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//用户取消拍摄\r\n\t\t\t\t\tif (retult.retult == 'cancel') {\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t)\r\n\t\t},\r\n\t\t// =====================\r\n\t\t// 创建发送输入框内容\r\n\t\tsendingText() {\r\n\t\t\tif (this.text === '')\r\n\t\t\t\treturn uni.showModal({\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tcontent: '不能发送空白信息',\r\n\t\t\t\t\tsuccess: function (res) {}\r\n\t\t\t\t})\r\n\t\t\tlet body = this.text\r\n\t\t\tif (this.text.length >= 50) {\r\n\t\t\t\tbody = this.text.substring(0, 30) + '...'\r\n\t\t\t}\r\n\t\t\tif (this.isQuote) {\r\n\t\t\t\tthis.createCustomMessageText(body)\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\ttext: this.text\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'text'\r\n\t\t\t})\r\n\r\n\t\t\tthis.text = ''\r\n\t\t},\r\n\r\n\t\t// 发送位置信息\r\n\t\tcreateCustomMessageMap(res, image) {\r\n\t\t\tconst { latitude, longitude, address, name } = res\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\tlatitude,\r\n\t\t\t\t\tlongitude,\r\n\t\t\t\t\ttitle: name,\r\n\t\t\t\t\taddress,\r\n\t\t\t\t\timage //使用高德api生成图片\r\n\t\t\t\t},\r\n\r\n\t\t\t\ttype: 'map'\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 引用并发送文本\r\n\t\tcreateCustomMessageText(body) {\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\ttext: this.text,\r\n\t\t\t\t\t//引用源\r\n\t\t\t\t\tquoteSource: {\r\n\t\t\t\t\t\t...this.quoteSource\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'text_quote'\r\n\t\t\t})\r\n\r\n\t\t\tthis.text = ''\r\n\t\t},\r\n\r\n\t\t// 创建发送照片内容\r\n\t\tsendImageMessage() {\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 9,\r\n\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\tres.tempFiles.forEach((file) => {\r\n\t\t\t\t\t\tconsole.log(file)\r\n\t\t\t\t\t\tthis.createImageMessage(file)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 创建发送照片内容\r\n\t\tcreateImageMessage(file) {\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\tcontentType: 'image/png',\r\n\t\t\t\t\tname: 'uni-image.png',\r\n\t\t\t\t\tsize: 82942,\r\n\t\t\t\t\turl: file.path,\r\n\t\t\t\t\twidth: 2732,\r\n\t\t\t\t\theight: 2732,\r\n\t\t\t\t\tthumbnail: file.path\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'image'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 创建发送视频内容\r\n\t\tsendVideoMessage() {\r\n\t\t\tuni.chooseVideo({\r\n\t\t\t\tsuccess: async (res) => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tthis.createVideoMessage(res)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tcreateVideoMessage(file) {\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\tvideo: {\r\n\t\t\t\t\t\tname: '3003009356267921_uni-video.mp4',\r\n\t\t\t\t\t\turl: file.tempFilePath,\r\n\t\t\t\t\t\twidth: 640,\r\n\t\t\t\t\t\theight: 352,\r\n\t\t\t\t\t\tcontentType: 'video/mp4',\r\n\t\t\t\t\t\tsize: 501774,\r\n\t\t\t\t\t\tduration: 8.32\r\n\t\t\t\t\t},\r\n\t\t\t\t\tthumbnail: {\r\n\t\t\t\t\t\tname: 'uni-thumbnail.jpg',\r\n\t\t\t\t\t\turl: '封面路径',\r\n\t\t\t\t\t\twidth: 364,\r\n\t\t\t\t\t\theight: 200,\r\n\t\t\t\t\t\tcontentType: 'image/jpg'\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'video'\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 创建语音内容\r\n\t\tsendingRecorder(file) {\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\tcontentType: 'audio/mp3',\r\n\t\t\t\t\tname: 'uni-audio.mp3',\r\n\t\t\t\t\tsize: 2357,\r\n\t\t\t\t\turl: file.tempFilePath,\r\n\t\t\t\t\tduration: 1.148\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'audio'\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 创建自定义表情包\r\n\t\tsendingEmojiPack(e) {\r\n\t\t\tthis.sendMessage({\r\n\t\t\t\tpayload: {\r\n\t\t\t\t\text: e.ext,\r\n\t\t\t\t\turl: e.url,\r\n\t\t\t\t\tpath: e.path,\r\n\t\t\t\t\ttext: e.text || '[表情包]'\r\n\t\t\t\t},\r\n\t\t\t\ttype: 'emoji_pack'\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t// 最终提交发送\r\n\t\tsendMessage({ payload, type }) {\r\n\t\t\tconst message = {\r\n\t\t\t\tgroupId: '22',\r\n\t\t\t\tsenderData: {},\r\n\t\t\t\tsenderId: 自己的信息.member_id,\r\n\t\t\t\tmessageId: Date.now(),\r\n\t\t\t\tpayload: payload,\r\n\t\t\t\ttimestamp: Date.now(),\r\n\t\t\t\ttype: type,\r\n\t\t\t\trecalled: false,\r\n\t\t\t\tstatus: 'success',\r\n\t\t\t\tisHide: 0\r\n\t\t\t}\r\n\t\t\tthis.$emit('pushList', message)\r\n\t\t\tthis.isQuote = false\r\n\t\t\tthis.quoteSource = {}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.bottom-operation-box {\r\n\tposition: relative;\r\n\tz-index: 9999;\r\n\twidth: 100vw;\r\n\tbackground-color: #f6f6f6;\r\n\r\n\t.line-break {\r\n\t\tposition: absolute;\r\n\t\tz-index: 99;\r\n\t\tleft: 0;\r\n\t\ttop: -58rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 60rpx;\r\n\t\tflex-direction: row-reverse;\r\n\r\n\t\t.line-break-box {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 160rpx;\r\n\t\t\theight: 100%;\r\n\t\t\tcolor: #2c2c2c;\r\n\t\t\tborder-radius: 20rpx 0 0 0;\r\n\t\t\tbackground-color: #f6f6f6;\r\n\r\n\t\t\t.line-break-icon {\r\n\t\t\t\twidth: 36rpx;\r\n\t\t\t\theight: 36rpx;\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.line-break-box::before {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: -60rpx;\r\n\t\t\ttop: 0;\r\n\t\t\tcontent: '';\r\n\t\t\twidth: 60rpx;\r\n\t\t\theight: 60rpx;\r\n\t\t\tdisplay: block;\r\n\t\t\ttext-align: center;\r\n\t\t\tbackground-image: radial-gradient(240rpx at 2rpx 0px, rgba(168, 195, 59, 0) 60rpx, #f6f6f6 60rpx);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.bottom-operation {\r\n\tbox-sizing: border-box;\r\n\tpadding: 14rpx 10rpx;\r\n\twidth: 100%;\r\n\talign-items: flex-end;\r\n\r\n\t.bottom-operation-icon {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\r\n\t\t.img {\r\n\t\t\twidth: 80%;\r\n\t\t\theight: 80%;\r\n\t\t}\r\n\t}\r\n\t.send-btn {\r\n\t\tcolor: white;\r\n\t\tbackground-color: #60be60;\r\n\t\tborder-radius: 8rpx;\r\n\t\tmargin: 0;\r\n\t\tline-height: normal;\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t}\r\n\r\n\t.bottom-operation-input {\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 10rpx 14rpx;\r\n\t\tmin-height: 84rpx;\r\n\t\tmax-height: 300rpx;\r\n\t\toverflow: auto;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t.input {\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin: 10rpx 0;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.keyboard {\r\n\ttransition: all 0.2s;\r\n}\r\n\r\n// 引用\r\n.quote {\r\n\tbox-sizing: border-box;\r\n\tpadding: 0 20rpx;\r\n\twidth: 100%;\r\n\theight: 50rpx;\r\n\tmargin-top: 8rpx;\r\n\tborder-radius: 10rpx;\r\n\tbackground-color: #eaeaea;\r\n\tcolor: #686868;\r\n\r\n\t.quote-row {\r\n\t\twidth: 200rpx;\r\n\t\ttext-overflow: ellipsis;\r\n\t\toverflow: auto;\r\n\t\twhite-space: nowrap;\r\n\r\n\t\t::v-deep .quote-box {\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 0;\r\n\t\t\tborder-radius: 0;\r\n\t\t\tmargin-top: 0;\r\n\t\t\tbackground-color: #eaeaea;\r\n\t\t\tcolor: #6b6b6b;\r\n\r\n\t\t\t.quote-name {\r\n\t\t\t}\r\n\r\n\t\t\t.m-image {\r\n\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t.img {\r\n\t\t\t\t\twidth: 40rpx;\r\n\t\t\t\t\theight: 40rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.quote-icon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754988902283\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}