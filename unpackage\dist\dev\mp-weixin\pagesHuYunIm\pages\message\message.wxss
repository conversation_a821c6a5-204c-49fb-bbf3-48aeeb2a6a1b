@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.message-page.data-v-0b926c58 {
  height: 100vh;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
}
.nav-bar.data-v-0b926c58 {
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1rpx solid #e5e5e5;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav-title.data-v-0b926c58 {
  font-size: 36rpx;
  font-weight: 600;
  color: #000000;
}
.message-list.data-v-0b926c58 {
  flex: 1;
  background-color: #ffffff;
}
.message-item.data-v-0b926c58 {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  transition: background-color 0.2s;
}
.message-item.data-v-0b926c58:active {
  background-color: #f5f5f5;
}
.message-item.data-v-0b926c58:last-child {
  border-bottom: none;
}
.avatar-container.data-v-0b926c58 {
  margin-right: 24rpx;
}
.avatar.data-v-0b926c58 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 12rpx;
  background-color: #f0f0f0;
}
.message-content.data-v-0b926c58 {
  flex: 1;
  min-width: 0;
}
.message-header.data-v-0b926c58 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}
.username.data-v-0b926c58 {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.time.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999999;
  flex-shrink: 0;
  margin-left: 16rpx;
}
.last-message.data-v-0b926c58 {
  display: flex;
  align-items: center;
}
.message-text.data-v-0b926c58 {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}
.unread-badge.data-v-0b926c58 {
  margin-left: 16rpx;
  background-color: #ff4444;
  border-radius: 20rpx;
  min-width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}
.unread-text.data-v-0b926c58 {
  font-size: 22rpx;
  color: #ffffff;
  font-weight: 500;
  line-height: 1;
}
@media screen and (max-width: 750rpx) {
.message-item.data-v-0b926c58 {
    padding: 20rpx 24rpx;
}
.avatar.data-v-0b926c58 {
    width: 88rpx;
    height: 88rpx;
}
.username.data-v-0b926c58 {
    font-size: 30rpx;
    max-width: 300rpx;
}
.message-text.data-v-0b926c58 {
    font-size: 26rpx;
}
}
