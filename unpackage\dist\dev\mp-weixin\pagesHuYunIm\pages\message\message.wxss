@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.chat-page.data-v-0b926c58 {
  height: 100vh;
  background-color: #f8f8f8;
  display: flex;
  flex-direction: column;
}
.nav-bar.data-v-0b926c58 {
  height: 88rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  border-bottom: 1rpx solid #e5e5e5;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav-left.data-v-0b926c58,
.nav-right.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-back.data-v-0b926c58 {
  font-size: 48rpx;
  color: #007aff;
  font-weight: 300;
}
.nav-more.data-v-0b926c58 {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}
.nav-center.data-v-0b926c58 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nav-title.data-v-0b926c58 {
  font-size: 32rpx;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
}
.nav-subtitle.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.2;
}
.message-list.data-v-0b926c58 {
  flex: 1;
  background-color: #f8f8f8;
  padding: 0 24rpx;
}
.load-more.data-v-0b926c58 {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.load-text.data-v-0b926c58 {
  font-size: 28rpx;
  color: #999999;
}
.time-divider.data-v-0b926c58 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 32rpx 0 16rpx;
}
.time-text.data-v-0b926c58 {
  background-color: rgba(0, 0, 0, 0.1);
  color: #ffffff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}
.message-wrapper.data-v-0b926c58 {
  margin-bottom: 24rpx;
}
.message-item.data-v-0b926c58 {
  display: flex;
  align-items: flex-start;
}
.message-item.is-self.data-v-0b926c58 {
  flex-direction: row-reverse;
}
.message-item.is-self .message-body.data-v-0b926c58 {
  align-items: flex-end;
}
.message-item.is-self .avatar-container.data-v-0b926c58 {
  margin-left: 16rpx;
  margin-right: 0;
}
.avatar-container.data-v-0b926c58 {
  margin-right: 16rpx;
}
.avatar.data-v-0b926c58 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}
.message-body.data-v-0b926c58 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: calc(100% - 120rpx);
}
.nickname.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
  padding: 0 16rpx;
}
.message-bubble.data-v-0b926c58 {
  max-width: 100%;
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  position: relative;
  word-wrap: break-word;
}
.message-bubble.bubble-self.data-v-0b926c58 {
  background-color: #95ec69;
  color: #000000;
}
.message-bubble.bubble-self.data-v-0b926c58::after {
  content: "";
  position: absolute;
  right: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-left-color: #95ec69;
}
.message-bubble.bubble-other.data-v-0b926c58 {
  background-color: #ffffff;
  color: #000000;
}
.message-bubble.bubble-other.data-v-0b926c58::before {
  content: "";
  position: absolute;
  left: -12rpx;
  top: 20rpx;
  width: 0;
  height: 0;
  border: 12rpx solid transparent;
  border-right-color: #ffffff;
}
.message-bubble.bubble-image.data-v-0b926c58 {
  padding: 8rpx;
  border-radius: 12rpx;
}
.text-content.data-v-0b926c58 {
  font-size: 32rpx;
  line-height: 1.4;
}
.image-content.data-v-0b926c58 {
  border-radius: 8rpx;
  overflow: hidden;
}
.message-image.data-v-0b926c58 {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
}
.voice-content.data-v-0b926c58 {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}
.voice-icon.data-v-0b926c58 {
  margin-right: 16rpx;
}
.voice-icon.playing.data-v-0b926c58 {
  -webkit-animation: voice-wave-data-v-0b926c58 1s infinite;
          animation: voice-wave-data-v-0b926c58 1s infinite;
}
.voice-waves.data-v-0b926c58 {
  font-size: 32rpx;
}
.voice-duration.data-v-0b926c58 {
  font-size: 28rpx;
  color: #666666;
}
.other-content.data-v-0b926c58 {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60rpx;
}
.other-text.data-v-0b926c58 {
  font-size: 28rpx;
  color: #666666;
}
.message-status.data-v-0b926c58 {
  margin-top: 8rpx;
  padding: 0 16rpx;
}
.status-text.data-v-0b926c58 {
  font-size: 24rpx;
  color: #999999;
}
.input-area.data-v-0b926c58 {
  background-color: #ffffff;
  border-top: 1rpx solid #e5e5e5;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
}
.input-container.data-v-0b926c58 {
  display: flex;
  align-items: flex-end;
  gap: 16rpx;
}
.input-tools.data-v-0b926c58 {
  display: flex;
  align-items: center;
}
.tool-item.data-v-0b926c58 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
.tool-item.data-v-0b926c58:active {
  background-color: #e5e5e5;
}
.tool-icon.data-v-0b926c58 {
  font-size: 32rpx;
}
.input-wrapper.data-v-0b926c58 {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  padding: 16rpx 20rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
}
.message-input.data-v-0b926c58 {
  width: 100%;
  font-size: 32rpx;
  line-height: 1.4;
  background-color: transparent;
  border: none;
  outline: none;
  resize: none;
  min-height: 32rpx;
  max-height: 120rpx;
}
.input-actions.data-v-0b926c58 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.action-item.data-v-0b926c58 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}
.action-item.data-v-0b926c58:active {
  background-color: #e5e5e5;
}
.action-icon.data-v-0b926c58 {
  font-size: 32rpx;
}
.send-btn.data-v-0b926c58 {
  background-color: #07c160;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  min-width: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.send-btn.data-v-0b926c58:active {
  background-color: #06ad56;
}
.send-text.data-v-0b926c58 {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}
@-webkit-keyframes voice-wave-data-v-0b926c58 {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@keyframes voice-wave-data-v-0b926c58 {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}
}
@media screen and (max-width: 750rpx) {
.nav-bar.data-v-0b926c58 {
    height: 80rpx;
    padding: 0 24rpx;
}
.nav-title.data-v-0b926c58 {
    font-size: 30rpx;
}
.nav-subtitle.data-v-0b926c58 {
    font-size: 22rpx;
}
.message-list.data-v-0b926c58 {
    padding: 0 20rpx;
}
.avatar.data-v-0b926c58 {
    width: 72rpx;
    height: 72rpx;
}
.text-content.data-v-0b926c58 {
    font-size: 30rpx;
}
.message-image.data-v-0b926c58 {
    width: 180rpx;
    height: 135rpx;
}
.input-area.data-v-0b926c58 {
    padding: 12rpx 20rpx;
}
.message-input.data-v-0b926c58 {
    font-size: 30rpx;
}
.send-text.data-v-0b926c58 {
    font-size: 30rpx;
}
}
