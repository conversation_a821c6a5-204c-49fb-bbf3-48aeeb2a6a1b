{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?6f9d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?7aaf", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?ba47", "uni-app:///pagesHuYunIm/pages/message/message.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?a4db", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?2605"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "messageList", "id", "createBy", "createTime", "updateBy", "updateTime", "sysOrgCode", "userId", "groupId", "msgType", "content", "status", "unreadCount", "userMap", "name", "avatar", "computed", "watch", "mounted", "methods", "onMessageTap", "console", "uni", "url", "getUserAvatar", "getUserName", "formatTime", "getMessageContent", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgChvB;EACAC;IACA;MACAC,cACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACA;MACAC;QACA;UACAC;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;QACA;UACAD;UACAC;QACA;MACA;IACA;EACA;EACAC;EACAC;EACAC;EACAC;IACA;IACAC;MACAC;MACA;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;;MAEA;MACA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAC;AACA;AAAA,2B;;;;;;;;;;;;;ACrMA;AAAA;AAAA;AAAA;AAA+3C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAn5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/message/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/message/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=0b926c58&scoped=true&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b926c58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/message/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=0b926c58&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.messageList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.getUserAvatar(item.userId)\n    var m1 = _vm.getUserName(item.userId)\n    var m2 = _vm.formatTime(item.createTime)\n    var m3 = _vm.getMessageContent(item)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"message-page\">\n    <scroll-view scroll-y>\n      <!-- 消息列表 -->\n      <view class=\"message-item\" v-for=\"(item, index) in messageList\" :key=\"item.id\" @tap=\"onMessageTap(item)\">\n        <!-- 头像 -->\n        <view class=\"avatar-container\">\n          <image class=\"avatar\" :src=\"getUserAvatar(item.userId)\" mode=\"aspectFill\" />\n        </view>\n        <!-- 消息内容区域 -->\n        <view class=\"message-content\">\n          <!-- 用户名和时间 -->\n          <view class=\"message-header\">\n            <text class=\"username\">{{ getUserName(item.userId) }}</text>\n            <text class=\"time\">{{ formatTime(item.createTime) }}</text>\n          </view>\n\n          <!-- 最后一条消息内容 -->\n          <view class=\"last-message\">\n            <text class=\"message-text\">{{ getMessageContent(item) }}</text>\n          </view>\n        </view>\n\n        <!-- 未读消息数量 -->\n        <view class=\"unread-badge\" v-if=\"item.unreadCount && item.unreadCount > 0\">\n          <text class=\"unread-text\">{{ item.unreadCount > 99 ? '99+' : item.unreadCount }}</text>\n        </view>\n      </view>\n    </scroll-view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      messageList: [\n        {\n          id: '1921827090039152642_000001',\n          createBy: null,\n          createTime: '2025-01-12 15:17:51',\n          updateBy: null,\n          updateTime: null,\n          sysOrgCode: null,\n          userId: '1921822887908581378',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '你好，最近怎么样？',\n          status: '正常',\n          unreadCount: 2\n        },\n        {\n          id: '1921827090039152642_000002',\n          createBy: null,\n          createTime: '2025-01-12 14:18:22',\n          updateBy: null,\n          updateTime: null,\n          sysOrgCode: null,\n          userId: '1921822887908581377',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '好的，没问题',\n          status: '正常',\n          unreadCount: 0\n        },\n        {\n          id: '1921827090039152642_000003',\n          createBy: null,\n          createTime: '2025-01-11 20:30:15',\n          updateBy: null,\n          updateTime: null,\n          sysOrgCode: null,\n          userId: '1921822887908581379',\n          groupId: '1921827090039152643',\n          msgType: 'image',\n          content: '',\n          status: '正常',\n          unreadCount: 5\n        },\n        {\n          id: '1921827090039152642_000004',\n          createBy: null,\n          createTime: '2025-01-11 16:45:30',\n          updateBy: null,\n          updateTime: null,\n          sysOrgCode: null,\n          userId: '1921822887908581380',\n          groupId: '1921827090039152644',\n          msgType: 'audio',\n          content: '',\n          status: '正常',\n          unreadCount: 1\n        },\n        {\n          id: '1921827090039152642_000005',\n          createBy: null,\n          createTime: '2025-01-10 09:20:45',\n          updateBy: null,\n          updateTime: null,\n          sysOrgCode: null,\n          userId: '1921822887908581381',\n          groupId: '1921827090039152645',\n          msgType: 'text',\n          content: '明天的会议记得参加哦',\n          status: '正常',\n          unreadCount: 0\n        }\n      ],\n      // 用户信息映射，实际项目中应该从接口获取\n      userMap: {\n        '1921822887908581378': {\n          name: '张三',\n          avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=张'\n        },\n        '1921822887908581377': {\n          name: '李四',\n          avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=李'\n        },\n        '1921822887908581379': {\n          name: '王五',\n          avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'\n        },\n        '1921822887908581380': {\n          name: '赵六',\n          avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'\n        },\n        '1921822887908581381': {\n          name: '项目组',\n          avatar: 'https://via.placeholder.com/96x96/607D8B/FFFFFF?text=组'\n        }\n      }\n    }\n  },\n  computed: {},\n  watch: {},\n  mounted() {},\n  methods: {\n    // 点击消息项\n    onMessageTap(item) {\n      console.log('点击消息:', item)\n      // 这里可以跳转到聊天页面\n      uni.navigateTo({\n        url: `/pagesHuYunIm/pages/chat/index?groupId=${item.groupId}&userId=${item.userId}`\n      })\n    },\n\n    // 获取用户头像\n    getUserAvatar(userId) {\n      return this.userMap[userId]?.avatar || 'https://via.placeholder.com/96x96/9E9E9E/FFFFFF?text=?'\n    },\n\n    // 获取用户名\n    getUserName(userId) {\n      return this.userMap[userId]?.name || '未知用户'\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return ''\n\n      const now = new Date()\n      const msgTime = new Date(timeStr)\n      const diff = now.getTime() - msgTime.getTime()\n\n      // 今天\n      if (diff < 24 * 60 * 60 * 1000 && now.getDate() === msgTime.getDate()) {\n        return msgTime.toTimeString().slice(0, 5) // HH:MM\n      }\n\n      // 昨天\n      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)\n      if (yesterday.getDate() === msgTime.getDate()) {\n        return '昨天'\n      }\n\n      // 更早的时间\n      return `${msgTime.getMonth() + 1}/${msgTime.getDate()}`\n    },\n\n    // 获取消息内容预览\n    getMessageContent(item) {\n      switch (item.msgType) {\n        case 'text':\n          return item.content || ''\n        case 'image':\n          return '[图片]'\n        case 'audio':\n          return '[语音]'\n        case 'video':\n          return '[视频]'\n        case 'file':\n          return '[文件]'\n        default:\n          return '[消息]'\n      }\n    }\n  },\n  onLoad() {}\n}\n</script>\n<style lang=\"scss\" scoped>\n.message-page {\n  height: 100vh;\n  background-color: #ffffff;\n  display: flex;\n  flex-direction: column;\n}\n\n.nav-bar {\n  height: 88rpx;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-bottom: 1rpx solid #e5e5e5;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.nav-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #000000;\n}\n\n.message-list {\n  flex: 1;\n  background-color: #ffffff;\n}\n\n.message-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 32rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  background-color: #ffffff;\n  transition: background-color 0.2s;\n\n  &:active {\n    background-color: #f5f5f5;\n  }\n\n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.avatar-container {\n  margin-right: 24rpx;\n}\n\n.avatar {\n  width: 96rpx;\n  height: 96rpx;\n  border-radius: 12rpx;\n  background-color: #f0f0f0;\n}\n\n.message-content {\n  flex: 1;\n  min-width: 0; // 防止文本溢出\n}\n\n.message-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8rpx;\n}\n\n.username {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #000000;\n  max-width: 400rpx;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.time {\n  font-size: 24rpx;\n  color: #999999;\n  flex-shrink: 0;\n  margin-left: 16rpx;\n}\n\n.last-message {\n  display: flex;\n  align-items: center;\n}\n\n.message-text {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.4;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  max-width: 100%;\n}\n\n.unread-badge {\n  margin-left: 16rpx;\n  background-color: #ff4444;\n  border-radius: 20rpx;\n  min-width: 36rpx;\n  height: 36rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 8rpx;\n}\n\n.unread-text {\n  font-size: 22rpx;\n  color: #ffffff;\n  font-weight: 500;\n  line-height: 1;\n}\n\n// 响应式适配\n@media screen and (max-width: 750rpx) {\n  .message-item {\n    padding: 20rpx 24rpx;\n  }\n\n  .avatar {\n    width: 88rpx;\n    height: 88rpx;\n  }\n\n  .username {\n    font-size: 30rpx;\n    max-width: 300rpx;\n  }\n\n  .message-text {\n    font-size: 26rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754990593590\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}