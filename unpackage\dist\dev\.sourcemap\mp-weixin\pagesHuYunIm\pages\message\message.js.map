{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?6f9d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?7aaf", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?ba47", "uni-app:///pagesHuYunIm/pages/message/message.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?a4db", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/message/message.vue?2605"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "groupInfo", "id", "name", "memberCount", "selfUserId", "selfAvatar", "messageList", "createTime", "userId", "groupId", "msgType", "content", "status", "userMap", "avatar", "inputText", "scrollTop", "loading", "isGroupChat", "showEmojiPanel", "page", "pageSize", "hasMore", "computed", "watch", "handler", "deep", "mounted", "methods", "goBack", "uni", "showGroupInfo", "console", "isSelfMessage", "getUserAvatar", "getUserName", "showTime", "formatMessageTime", "getBubbleClass", "classes", "getMessageContent", "showUserProfile", "showMessageMenu", "itemList", "success", "copyMessage", "title", "icon", "deleteMessage", "recallMessage", "item", "previewImage", "urls", "current", "loadMoreMessages", "setTimeout", "onInputFocus", "onInputBlur", "sendMessage", "message", "chooseImage", "count", "sourceType", "sendImageMessage", "recordVoice", "handleShowEmojiPanel", "scrollToBottom", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;;;ACHA;AAAA;AAAA;AAAA;AAA4tB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuHhvB;EACAC;IACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;MACAC;MAEA;MACAC,cACA;QACAL;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAX;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MAEA;MACAC;QACA;UACAX;UACAY;QACA;QACA;UACAZ;UACAY;QACA;QACA;UACAZ;UACAY;QACA;QACA;UACAZ;UACAY;QACA;QACA;UACAZ;UACAY;QACA;MACA;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EAEAC;IACAlB;MACAmB;QAAA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MACAC;IACA;IAEA;IACAC;MACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;IACAC;MAAA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MACA;MAEA;MACA;MACA;MACA;MAEA;MAEA;QACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;MAEA;QACAA;MACA;MAEA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACAT;MACA;IACA;IAEA;IACAU;MAAA;MACAV;MACAF;QACAa;QACAC;UACA;YACA;cACA;cACA;YACA;cACA;cACA;YACA;cACA;cACA;UAAA;QAEA;MACA;IACA;IAEA;IACAC;MACA;QACAf;UACA/B;UACA6C;YACAd;cACAgB;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAlB;QACAgB;QACAnC;QACAiC;UACA;YACA;cAAA;YAAA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MACA;QACAnB;UACAgB;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;QACAjB;UACAgB;UACAC;QACA;QACA;MACA;MAEAjB;QACAgB;QACAnC;QACAiC;UACA;YACA;YACAM;YACApB;cACAgB;cACAC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAI;MACArB;QACAsB;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MAEA;MACAtB;;MAEA;MACAuB;QACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;QACA5B;UACAgB;UACAC;QACA;QACA;MACA;MAEA;QACA9C;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;;MAEA;MACA;MACA;;MAEA;MACA2C;QACAI;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA9B;QACA+B;QACAC;QACAlB;UACA;UACA;QACA;MACA;IACA;IAEA;IACAmB;MACA;QACA9D;QACAM;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;;MAEA;MACA2C;QACAI;QACA;MACA;IACA;IAEA;IACAK;MACAlC;QACAgB;QACAC;MACA;IACA;IAEA;IACAkB;MACAnC;QACAgB;QACAC;MACA;IACA;IAEA;IACAmB;MAAA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpjBA;AAAA;AAAA;AAAA;AAA+3C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACAn5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/message/message.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/message/message.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./message.vue?vue&type=template&id=0b926c58&scoped=true&\"\nvar renderjs\nimport script from \"./message.vue?vue&type=script&lang=js&\"\nexport * from \"./message.vue?vue&type=script&lang=js&\"\nimport style0 from \"./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b926c58\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/message/message.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=template&id=0b926c58&scoped=true&\"", "var render = function () {}\nvar staticRenderFns = []\nvar recyclableRender\nvar components\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"chat-page\">\n    <!-- 顶部导航栏 -->\n    <view class=\"nav-bar\">\n      <view class=\"nav-left\" @tap=\"goBack\">\n        <text class=\"nav-back\">‹</text>\n      </view>\n      <view class=\"nav-center\">\n        <text class=\"nav-title\">{{ groupInfo.name }}</text>\n        <text class=\"nav-subtitle\">{{ groupInfo.memberCount }}人</text>\n      </view>\n      <view class=\"nav-right\" @tap=\"showGroupInfo\">\n        <text class=\"nav-more\">⋯</text>\n      </view>\n    </view>\n\n    <!-- 消息列表 -->\n    <scroll-view class=\"message-list\" scroll-y :scroll-top=\"scrollTop\" scroll-with-animation @scrolltoupper=\"loadMoreMessages\">\n      <!-- 加载更多提示 -->\n      <view class=\"load-more\" v-if=\"loading\">\n        <text class=\"load-text\">加载中...</text>\n      </view>\n\n      <!-- 消息项 -->\n      <view class=\"message-wrapper\" v-for=\"(item, index) in messageList\" :key=\"item.id\">\n        <!-- 时间分隔线 -->\n        <view class=\"time-divider\" v-if=\"showTime(item, index)\">\n          <text class=\"time-text\">{{ formatMessageTime(item.createTime) }}</text>\n        </view>\n        <!-- 消息内容 -->\n        <view class=\"message-item\" :class=\"{ 'is-self': isSelfMessage(item) }\">\n          <!-- 左侧头像（对方消息） -->\n          <view class=\"avatar-container\" v-if=\"!isSelfMessage(item)\">\n            <image class=\"avatar\" :src=\"getUserAvatar(item.userId)\" mode=\"aspectFill\" @tap=\"showUserProfile(item.userId)\" />\n          </view>\n          <!-- 消息主体 -->\n          <view class=\"message-body\">\n            <!-- 昵称（群聊中对方消息显示） -->\n            <view class=\"nickname\" v-if=\"!isSelfMessage(item) && isGroupChat\">\n              {{ getUserName(item.userId) }}\n            </view>\n            <!-- 消息气泡 -->\n            <view class=\"message-bubble\" :class=\"getBubbleClass(item)\" @longpress=\"showMessageMenu(item)\">\n              <!-- 文本消息 -->\n              <view class=\"text-content\" v-if=\"item.msgType === 'text'\">\n                {{ item.content }}\n              </view>\n              <!-- 图片消息 -->\n              <view class=\"image-content\" v-else-if=\"item.msgType === 'image'\">\n                <image class=\"message-image\" :src=\"item.content\" mode=\"aspectFill\" @tap=\"previewImage(item.content)\" />\n              </view>\n\n              <!-- 语音消息 -->\n              <view class=\"voice-content\" v-else-if=\"item.msgType === 'audio'\">\n                <view class=\"voice-icon\" :class=\"{ playing: item.isPlaying }\">\n                  <text class=\"voice-waves\">🎵</text>\n                </view>\n                <text class=\"voice-duration\">{{ item.duration || '1' }}\"</text>\n              </view>\n\n              <!-- 其他类型消息 -->\n              <view class=\"other-content\" v-else>\n                <text class=\"other-text\">{{ getMessageContent(item) }}</text>\n              </view>\n            </view>\n\n            <!-- 消息状态（自己的消息） -->\n            <view class=\"message-status\" v-if=\"isSelfMessage(item)\">\n              <text class=\"status-text\" v-if=\"item.status === 'sending'\">发送中</text>\n              <text class=\"status-text\" v-else-if=\"item.status === 'failed'\">发送失败</text>\n            </view>\n          </view>\n\n          <!-- 右侧头像（自己的消息） -->\n          <view class=\"avatar-container\" v-if=\"isSelfMessage(item)\">\n            <image class=\"avatar\" :src=\"selfAvatar\" mode=\"aspectFill\" />\n          </view>\n        </view>\n      </view>\n    </scroll-view>\n\n    <!-- 底部输入区域 -->\n    <view class=\"input-area\">\n      <view class=\"input-container\">\n        <view class=\"input-tools\">\n          <view class=\"tool-item\" @tap=\"handleShowEmojiPanel\">\n            <text class=\"tool-icon\">😊</text>\n          </view>\n        </view>\n\n        <view class=\"input-wrapper\">\n          <textarea\n            class=\"message-input\"\n            v-model=\"inputText\"\n            placeholder=\"输入消息...\"\n            :auto-height=\"true\"\n            :max-height=\"120\"\n            @focus=\"onInputFocus\"\n            @blur=\"onInputBlur\"\n            @confirm=\"sendMessage\"\n          />\n        </view>\n\n        <view class=\"input-actions\">\n          <view class=\"action-item\" @tap=\"chooseImage\" v-if=\"!inputText.trim()\">\n            <text class=\"action-icon\">📷</text>\n          </view>\n          <view class=\"action-item\" @tap=\"recordVoice\" v-if=\"!inputText.trim()\">\n            <text class=\"action-icon\">🎤</text>\n          </view>\n          <view class=\"send-btn\" @tap=\"sendMessage\" v-if=\"inputText.trim()\">\n            <text class=\"send-text\">发送</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      // 群聊信息\n      groupInfo: {\n        id: '1921827090039152642',\n        name: '项目讨论组',\n        memberCount: 5\n      },\n\n      // 当前用户信息\n      selfUserId: '1921822887908581378',\n      selfAvatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=我',\n\n      // 消息列表\n      messageList: [\n        {\n          id: '1921827090039152642_000001',\n          createTime: '2025-01-12 15:17:51',\n          userId: '1921822887908581377',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '大家好，今天的项目进度怎么样？',\n          status: 'success'\n        },\n        {\n          id: '1921827090039152642_000002',\n          createTime: '2025-01-12 15:18:22',\n          userId: '1921822887908581378',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '我这边基本完成了，正在测试',\n          status: 'success'\n        },\n        {\n          id: '1921827090039152642_000003',\n          createTime: '2025-01-12 15:19:15',\n          userId: '1921822887908581379',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '我还需要一点时间，预计明天完成',\n          status: 'success'\n        },\n        {\n          id: '1921827090039152642_000004',\n          createTime: '2025-01-12 15:20:30',\n          userId: '1921822887908581380',\n          groupId: '1921827090039152642',\n          msgType: 'image',\n          content: 'https://via.placeholder.com/200x150/FF5722/FFFFFF?text=截图',\n          status: 'success'\n        },\n        {\n          id: '1921827090039152642_000005',\n          createTime: '2025-01-12 15:21:45',\n          userId: '1921822887908581378',\n          groupId: '1921827090039152642',\n          msgType: 'text',\n          content: '看起来不错！',\n          status: 'success'\n        }\n      ],\n\n      // 用户信息映射\n      userMap: {\n        '1921822887908581377': {\n          name: '张三',\n          avatar: 'https://via.placeholder.com/96x96/2196F3/FFFFFF?text=张'\n        },\n        '1921822887908581378': {\n          name: '李四',\n          avatar: 'https://via.placeholder.com/96x96/4CAF50/FFFFFF?text=李'\n        },\n        '1921822887908581379': {\n          name: '王五',\n          avatar: 'https://via.placeholder.com/96x96/FF9800/FFFFFF?text=王'\n        },\n        '1921822887908581380': {\n          name: '赵六',\n          avatar: 'https://via.placeholder.com/96x96/9C27B0/FFFFFF?text=赵'\n        },\n        '1921822887908581381': {\n          name: '项目组',\n          avatar: 'https://via.placeholder.com/96x96/607D8B/FFFFFF?text=组'\n        }\n      },\n\n      // 界面状态\n      inputText: '',\n      scrollTop: 0,\n      loading: false,\n      isGroupChat: true,\n      showEmojiPanel: false,\n\n      // 分页参数\n      page: 1,\n      pageSize: 20,\n      hasMore: true\n    }\n  },\n\n  computed: {},\n\n  watch: {\n    messageList: {\n      handler() {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      },\n      deep: true\n    }\n  },\n\n  mounted() {\n    this.scrollToBottom()\n  },\n\n  methods: {\n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    },\n\n    // 显示群信息\n    showGroupInfo() {\n      console.log('显示群信息')\n      // 这里可以跳转到群信息页面\n    },\n\n    // 判断是否为自己发送的消息\n    isSelfMessage(item) {\n      return item.userId === this.selfUserId\n    },\n\n    // 获取用户头像\n    getUserAvatar(userId) {\n      return this.userMap[userId]?.avatar || 'https://via.placeholder.com/96x96/9E9E9E/FFFFFF?text=?'\n    },\n\n    // 获取用户名\n    getUserName(userId) {\n      return this.userMap[userId]?.name || '未知用户'\n    },\n\n    // 是否显示时间分隔线\n    showTime(item, index) {\n      if (index === 0) return true\n\n      const prevItem = this.messageList[index - 1]\n      const currentTime = new Date(item.createTime).getTime()\n      const prevTime = new Date(prevItem.createTime).getTime()\n\n      // 超过5分钟显示时间\n      return currentTime - prevTime > 5 * 60 * 1000\n    },\n\n    // 格式化消息时间\n    formatMessageTime(timeStr) {\n      if (!timeStr) return ''\n\n      const now = new Date()\n      const msgTime = new Date(timeStr)\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())\n      const msgDate = new Date(msgTime.getFullYear(), msgTime.getMonth(), msgTime.getDate())\n\n      const diffDays = Math.floor((today - msgDate) / (24 * 60 * 60 * 1000))\n\n      if (diffDays === 0) {\n        // 今天，显示时间\n        return msgTime.toTimeString().slice(0, 5)\n      } else if (diffDays === 1) {\n        // 昨天\n        return `昨天 ${msgTime.toTimeString().slice(0, 5)}`\n      } else if (diffDays < 7) {\n        // 一周内，显示星期\n        const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\n        return `${weekdays[msgTime.getDay()]} ${msgTime.toTimeString().slice(0, 5)}`\n      } else {\n        // 更早，显示日期\n        return `${msgTime.getMonth() + 1}月${msgTime.getDate()}日 ${msgTime.toTimeString().slice(0, 5)}`\n      }\n    },\n\n    // 获取消息气泡样式类\n    getBubbleClass(item) {\n      const classes = []\n      if (this.isSelfMessage(item)) {\n        classes.push('bubble-self')\n      } else {\n        classes.push('bubble-other')\n      }\n\n      if (item.msgType === 'image') {\n        classes.push('bubble-image')\n      }\n\n      return classes.join(' ')\n    },\n\n    // 获取消息内容预览\n    getMessageContent(item) {\n      switch (item.msgType) {\n        case 'text':\n          return item.content || ''\n        case 'image':\n          return '[图片]'\n        case 'audio':\n          return '[语音]'\n        case 'video':\n          return '[视频]'\n        case 'file':\n          return '[文件]'\n        default:\n          return '[消息]'\n      }\n    },\n\n    // 显示用户资料\n    showUserProfile(userId) {\n      console.log('显示用户资料:', userId)\n      // 这里可以跳转到用户资料页面\n    },\n\n    // 显示消息菜单\n    showMessageMenu(item) {\n      console.log('显示消息菜单:', item)\n      uni.showActionSheet({\n        itemList: ['复制', '删除', '撤回'],\n        success: (res) => {\n          switch (res.tapIndex) {\n            case 0:\n              this.copyMessage(item)\n              break\n            case 1:\n              this.deleteMessage(item)\n              break\n            case 2:\n              this.recallMessage(item)\n              break\n          }\n        }\n      })\n    },\n\n    // 复制消息\n    copyMessage(item) {\n      if (item.msgType === 'text') {\n        uni.setClipboardData({\n          data: item.content,\n          success: () => {\n            uni.showToast({\n              title: '已复制',\n              icon: 'success'\n            })\n          }\n        })\n      }\n    },\n\n    // 删除消息\n    deleteMessage(item) {\n      uni.showModal({\n        title: '确认删除',\n        content: '确定要删除这条消息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            const index = this.messageList.findIndex((msg) => msg.id === item.id)\n            if (index > -1) {\n              this.messageList.splice(index, 1)\n            }\n          }\n        }\n      })\n    },\n\n    // 撤回消息\n    recallMessage(item) {\n      if (!this.isSelfMessage(item)) {\n        uni.showToast({\n          title: '只能撤回自己的消息',\n          icon: 'none'\n        })\n        return\n      }\n\n      // 检查时间限制（比如2分钟内）\n      const now = new Date().getTime()\n      const msgTime = new Date(item.createTime).getTime()\n      if (now - msgTime > 2 * 60 * 1000) {\n        uni.showToast({\n          title: '超过时间限制，无法撤回',\n          icon: 'none'\n        })\n        return\n      }\n\n      uni.showModal({\n        title: '确认撤回',\n        content: '确定要撤回这条消息吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 这里应该调用撤回消息的API\n            item.recalled = true\n            uni.showToast({\n              title: '已撤回',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    },\n\n    // 预览图片\n    previewImage(url) {\n      uni.previewImage({\n        urls: [url],\n        current: url\n      })\n    },\n\n    // 加载更多消息\n    loadMoreMessages() {\n      if (this.loading || !this.hasMore) return\n\n      this.loading = true\n      console.log('加载更多消息...')\n\n      // 模拟加载更多消息\n      setTimeout(() => {\n        this.loading = false\n        // 这里应该调用API加载历史消息\n      }, 1000)\n    },\n\n    // 输入框获得焦点\n    onInputFocus() {\n      this.$nextTick(() => {\n        this.scrollToBottom()\n      })\n    },\n\n    // 输入框失去焦点\n    onInputBlur() {\n      // 可以在这里处理一些逻辑\n    },\n\n    // 发送消息\n    sendMessage() {\n      const text = this.inputText.trim()\n      if (!text) {\n        uni.showToast({\n          title: '请输入消息内容',\n          icon: 'none'\n        })\n        return\n      }\n\n      const message = {\n        id: Date.now().toString(),\n        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\n        userId: this.selfUserId,\n        groupId: this.groupInfo.id,\n        msgType: 'text',\n        content: text,\n        status: 'sending'\n      }\n\n      // 添加到消息列表\n      this.messageList.push(message)\n      this.inputText = ''\n\n      // 模拟发送过程\n      setTimeout(() => {\n        message.status = 'success'\n        // 这里应该调用发送消息的API\n      }, 1000)\n    },\n\n    // 选择图片\n    chooseImage() {\n      uni.chooseImage({\n        count: 1,\n        sourceType: ['camera', 'album'],\n        success: (res) => {\n          const tempFilePath = res.tempFilePaths[0]\n          this.sendImageMessage(tempFilePath)\n        }\n      })\n    },\n\n    // 发送图片消息\n    sendImageMessage(imagePath) {\n      const message = {\n        id: Date.now().toString(),\n        createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\n        userId: this.selfUserId,\n        groupId: this.groupInfo.id,\n        msgType: 'image',\n        content: imagePath,\n        status: 'sending'\n      }\n\n      this.messageList.push(message)\n\n      // 模拟上传过程\n      setTimeout(() => {\n        message.status = 'success'\n        // 这里应该调用上传图片和发送消息的API\n      }, 2000)\n    },\n\n    // 录制语音\n    recordVoice() {\n      uni.showToast({\n        title: '长按录音功能待开发',\n        icon: 'none'\n      })\n    },\n\n    // 显示表情面板\n    handleShowEmojiPanel() {\n      uni.showToast({\n        title: '表情功能待开发',\n        icon: 'none'\n      })\n    },\n\n    // 滚动到底部\n    scrollToBottom() {\n      this.$nextTick(() => {\n        this.scrollTop = this.messageList.length * 1000\n      })\n    }\n  },\n\n  onLoad(options) {\n    // 从参数中获取群组信息\n    if (options.groupId) {\n      this.groupInfo.id = options.groupId\n    }\n    if (options.groupName) {\n      this.groupInfo.name = decodeURIComponent(options.groupName)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n.chat-page {\n  height: 100vh;\n  background-color: #f8f8f8;\n  display: flex;\n  flex-direction: column;\n}\n\n// 顶部导航栏\n.nav-bar {\n  height: 88rpx;\n  background-color: #ffffff;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 32rpx;\n  border-bottom: 1rpx solid #e5e5e5;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.nav-left,\n.nav-right {\n  width: 80rpx;\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-back {\n  font-size: 48rpx;\n  color: #007aff;\n  font-weight: 300;\n}\n\n.nav-more {\n  font-size: 32rpx;\n  color: #007aff;\n  font-weight: bold;\n}\n\n.nav-center {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.nav-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #000000;\n  line-height: 1.2;\n}\n\n.nav-subtitle {\n  font-size: 24rpx;\n  color: #999999;\n  line-height: 1.2;\n}\n\n// 消息列表\n.message-list {\n  flex: 1;\n  background-color: #f8f8f8;\n  padding: 0 24rpx;\n}\n\n.load-more {\n  height: 80rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.load-text {\n  font-size: 28rpx;\n  color: #999999;\n}\n\n// 时间分隔线\n.time-divider {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 32rpx 0 16rpx;\n}\n\n.time-text {\n  background-color: rgba(0, 0, 0, 0.1);\n  color: #ffffff;\n  font-size: 24rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 16rpx;\n}\n\n// 消息项\n.message-wrapper {\n  margin-bottom: 24rpx;\n}\n\n.message-item {\n  display: flex;\n  align-items: flex-start;\n\n  &.is-self {\n    flex-direction: row-reverse;\n\n    .message-body {\n      align-items: flex-end;\n    }\n\n    .avatar-container {\n      margin-left: 16rpx;\n      margin-right: 0;\n    }\n  }\n}\n\n.avatar-container {\n  margin-right: 16rpx;\n}\n\n.avatar {\n  width: 80rpx;\n  height: 80rpx;\n  border-radius: 8rpx;\n  background-color: #f0f0f0;\n}\n\n.message-body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  max-width: calc(100% - 120rpx);\n}\n\n.nickname {\n  font-size: 24rpx;\n  color: #999999;\n  margin-bottom: 8rpx;\n  padding: 0 16rpx;\n}\n\n// 消息气泡\n.message-bubble {\n  max-width: 100%;\n  border-radius: 16rpx;\n  padding: 16rpx 20rpx;\n  position: relative;\n  word-wrap: break-word;\n\n  &.bubble-self {\n    background-color: #95ec69;\n    color: #000000;\n\n    &::after {\n      content: '';\n      position: absolute;\n      right: -12rpx;\n      top: 20rpx;\n      width: 0;\n      height: 0;\n      border: 12rpx solid transparent;\n      border-left-color: #95ec69;\n    }\n  }\n\n  &.bubble-other {\n    background-color: #ffffff;\n    color: #000000;\n\n    &::before {\n      content: '';\n      position: absolute;\n      left: -12rpx;\n      top: 20rpx;\n      width: 0;\n      height: 0;\n      border: 12rpx solid transparent;\n      border-right-color: #ffffff;\n    }\n  }\n\n  &.bubble-image {\n    padding: 8rpx;\n    border-radius: 12rpx;\n  }\n}\n\n.text-content {\n  font-size: 32rpx;\n  line-height: 1.4;\n}\n\n.image-content {\n  border-radius: 8rpx;\n  overflow: hidden;\n}\n\n.message-image {\n  width: 200rpx;\n  height: 150rpx;\n  border-radius: 8rpx;\n}\n\n.voice-content {\n  display: flex;\n  align-items: center;\n  min-width: 120rpx;\n}\n\n.voice-icon {\n  margin-right: 16rpx;\n\n  &.playing {\n    animation: voice-wave 1s infinite;\n  }\n}\n\n.voice-waves {\n  font-size: 32rpx;\n}\n\n.voice-duration {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n.other-content {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 60rpx;\n}\n\n.other-text {\n  font-size: 28rpx;\n  color: #666666;\n}\n\n.message-status {\n  margin-top: 8rpx;\n  padding: 0 16rpx;\n}\n\n.status-text {\n  font-size: 24rpx;\n  color: #999999;\n}\n\n// 底部输入区域\n.input-area {\n  background-color: #ffffff;\n  border-top: 1rpx solid #e5e5e5;\n  padding: 16rpx 24rpx;\n  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));\n}\n\n.input-container {\n  display: flex;\n  align-items: flex-end;\n  gap: 16rpx;\n}\n\n.input-tools {\n  display: flex;\n  align-items: center;\n}\n\n.tool-item {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8rpx;\n  background-color: #f5f5f5;\n\n  &:active {\n    background-color: #e5e5e5;\n  }\n}\n\n.tool-icon {\n  font-size: 32rpx;\n}\n\n.input-wrapper {\n  flex: 1;\n  background-color: #f5f5f5;\n  border-radius: 8rpx;\n  padding: 16rpx 20rpx;\n  min-height: 64rpx;\n  display: flex;\n  align-items: center;\n}\n\n.message-input {\n  width: 100%;\n  font-size: 32rpx;\n  line-height: 1.4;\n  background-color: transparent;\n  border: none;\n  outline: none;\n  resize: none;\n  min-height: 32rpx;\n  max-height: 120rpx;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.action-item {\n  width: 64rpx;\n  height: 64rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 8rpx;\n  background-color: #f5f5f5;\n\n  &:active {\n    background-color: #e5e5e5;\n  }\n}\n\n.action-icon {\n  font-size: 32rpx;\n}\n\n.send-btn {\n  background-color: #07c160;\n  border-radius: 8rpx;\n  padding: 16rpx 24rpx;\n  min-width: 100rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:active {\n    background-color: #06ad56;\n  }\n}\n\n.send-text {\n  color: #ffffff;\n  font-size: 32rpx;\n  font-weight: 500;\n}\n\n// 动画效果\n@keyframes voice-wave {\n  0%,\n  100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.2);\n  }\n}\n\n// 响应式适配\n@media screen and (max-width: 750rpx) {\n  .nav-bar {\n    height: 80rpx;\n    padding: 0 24rpx;\n  }\n\n  .nav-title {\n    font-size: 30rpx;\n  }\n\n  .nav-subtitle {\n    font-size: 22rpx;\n  }\n\n  .message-list {\n    padding: 0 20rpx;\n  }\n\n  .avatar {\n    width: 72rpx;\n    height: 72rpx;\n  }\n\n  .text-content {\n    font-size: 30rpx;\n  }\n\n  .message-image {\n    width: 180rpx;\n    height: 135rpx;\n  }\n\n  .input-area {\n    padding: 12rpx 20rpx;\n  }\n\n  .message-input {\n    font-size: 30rpx;\n  }\n\n  .send-text {\n    font-size: 30rpx;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./message.vue?vue&type=style&index=0&id=0b926c58&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754991264505\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}